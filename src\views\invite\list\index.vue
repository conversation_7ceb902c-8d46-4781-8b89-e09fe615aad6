<template>
  <div class="invite-list-container">
    <!-- 按钮 -->
    <el-card class="search-card">
      <!-- 搜索表单 -->
      <el-form
        ref="queryForm"
        :model="queryParams"
        :inline="true"
      >
        <el-form-item
          label="手机号码"
          prop="tell"
        >
          <el-input
            v-model="queryParams.tell"
            placeholder="请输入手机号码"
            clearable
            :prefix-icon="Phone"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item
          label="备注"
          prop="nick_name"
        >
          <el-input
            v-model="queryParams.nick_name"
            placeholder="请输入备注"
            clearable
            :prefix-icon="User"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            :icon="Search"
            @click="handleQuery"
          >
            搜索
          </el-button>
          <el-button
            :icon="Refresh"
            @click="resetQuery"
          >
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card class="box-card">
      <!-- 数据表格 -->
      <el-table
        v-loading="loading"
        :data="inviteList"
        style="width: 100%"
        border
      >
        <el-table-column
          type="index"
          label="序号"
          width="80"
        />
        <el-table-column
          prop="tell"
          label="用户手机号"
          width="180"
        />
        <el-table-column
          prop="id"
          label="用户id"
          width="100"
        />
        <el-table-column
          prop="created_at"
          label="注册时间"
        >
          <template #default="scope">
            {{ formatDate(scope.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="expire_at"
          label="到期时间"
        >
          <template #default="scope">
            {{ formatDate(scope.row.user_package.EndTime) }}
          </template>
        </el-table-column>
        <!-- 是否到期 -->
        <el-table-column
          label="是否到期"
          width="100"
        >
          <template #default="scope">
            <el-tag
              :type="isDateBeforeCurrent(scope.row.user_package.EndTime) ? 'danger' : 'success'"
            >
              {{ isDateBeforeCurrent(scope.row.user_package.EndTime) ? '已到期' : '未到期' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="备注"
          width="300"
        >
          <template #default="scope">
            <div
              v-if="editingId !== scope.row.id"
              class="remark-cell"
            >
              {{ scope.row.nick_name }}
              <el-icon
                class="edit-icon"
                @click="startEdit(scope.row)"
              >
                <Edit />
              </el-icon>
            </div>
            <div
              v-else
              class="edit-input"
            >
              <el-input
                v-model="editingName"
                size="small"
                @keyup.enter="saveEdit(scope.row)"
              >
                <template #append>
                  <el-button
                    class="action-button"
                    type="primary"
                    size="small"
                    @click="saveEdit(scope.row)"
                  >
                    确认
                  </el-button>
                  <el-button
                    class="action-button"
                    type="default"
                    size="small"
                    @click="cancelEdit"
                  >
                    取消
                  </el-button>
                </template>
              </el-input>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="queryParams.page"
          v-model:page-size="queryParams.page_size"
          :page-sizes="[10, 20, 30, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
/**
 * 邀请列表组件
 * 展示用户的邀请记录，支持按手机号和昵称筛选
 */
import { reactive, ref, onMounted } from 'vue';
import { getUserInviteList, InviteRecord } from '@/api/invite';
import { updateUser } from '@/api/plugin/user';
import { ElMessage } from 'element-plus';
import { Phone, User, Search, Refresh, Edit } from '@element-plus/icons-vue';
import { formatDate, isDateBeforeCurrent } from '@/utils/tools';

// 查询参数
const queryParams = reactive({
  page: 1,
  page_size: 10,
  tell: '',
  nick_name: '',
});

// 邀请列表数据
const inviteList = ref<InviteRecord[]>([]);
// 总数据条数
const total = ref(0);
// 加载状态
const loading = ref(false);
// 编辑状态管理
const editingId = ref<number | null>(null);
const editingName = ref('');

// 初始化
onMounted(() => {
  getList();
});

// 获取列表数据
const getList = async () => {
  loading.value = true;
  try {
    const response = await getUserInviteList(queryParams);
    // response是接口直接返回的数据，已经经过了axios拦截器处理
    inviteList.value = response.list || [];
    total.value = response.total || 0;
  } catch (error) {
    console.error('获取邀请列表失败:', error);
  } finally {
    loading.value = false;
  }
};

// 处理查询按钮点击
const handleQuery = () => {
  queryParams.page = 1;
  getList();
};

// 重置查询条件
const resetQuery = () => {
  queryParams.tell = '';
  queryParams.nick_name = '';
  handleQuery();
};

// 处理每页条数变化
const handleSizeChange = (pageSize: number) => {
  queryParams.page_size = pageSize;
  getList();
};

// 处理页码变化
const handleCurrentChange = (page: number) => {
  queryParams.page = page;
  getList();
};

// 开始编辑
const startEdit = (row: InviteRecord) => {
  editingId.value = row.id;
  editingName.value = row.nick_name;
};

// 取消编辑
const cancelEdit = () => {
  editingId.value = null;
  editingName.value = '';
};

// 保存编辑
const saveEdit = async (row: InviteRecord) => {
  if (!editingName.value.trim()) {
    ElMessage.warning('备注不能为空');
    return;
  }

  try {
    await updateUser({
      id: row.id,
      nick_name: editingName.value,
    });

    // 更新本地数据
    const index = inviteList.value.findIndex(item => item.id === row.id);
    if (index !== -1) {
      inviteList.value[index].nick_name = editingName.value;
    }

    ElMessage.success('备注修改成功');
    cancelEdit();
  } catch (error) {
    console.error('修改备注失败:', error);
    ElMessage.error('修改备注失败');
  }
};
</script>

<style scoped>
.invite-list-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.search-card {
  margin-bottom: 20px;
}

.remark-cell {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.edit-icon {
  cursor: pointer;
  color: #409eff;
}

.edit-input {
  width: 100%;
}

.action-button {
  margin-left: 10px;
  margin-right: 10px;
}
</style>
