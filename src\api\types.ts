// 统计数据接口
export interface StatsData {
  userCount: number;
  orderCount: number;
  favoriteCount: number;
  articleCount: number;
  userIncrease: number;
  orderIncrease: number;
  favoriteIncrease: number;
  articleIncrease: number;
}

// 图表数据接口
export interface ChartData {
  categories: string[];
  series: Array<{
    name: string;
    data: number[];
  }>;
}

// 待办事项接口
export interface TodoItem {
  id: string;
  title: string;
  completed: boolean;
  createTime: string;
}

// 系统通知接口
export interface NoticeItem {
  id: string;
  title: string;
  content: string;
  type: 'primary' | 'success' | 'warning' | 'danger' | 'info';
  read: boolean;
  createTime: string;
}

// 用户排行榜项接口
export interface UserRankingItem {
  id: number;
  username: string;
  amount: number;
  rank: number;
}

// 分页参数接口
export interface PageParams {
  pageNum: number;
  pageSize: number;
}

// 分页结果接口
export interface PageResult<T> {
  records: T[];
  total: number;
  pages: number;
  size: number;
  current: number;
}
