/**
 * 部门管理API模块
 * 提供部门相关的增删改查接口
 * 遵循RESTful设计规范
 * @module api/system/department
 */
import request from '@/utils/request';

/**
 * 部门数据接口
 * @interface Department
 */
export interface Department {
  /** 部门ID */
  id?: number;
  /** 部门名称 */
  name: string;
  /** 父部门ID */
  parent_id: number | null;
  /** 子部门 */
  children?: Department[];
}

/**
 * 获取部门树形结构
 * @returns {Promise<Department[]>} 部门树形结构
 */
export function getDepartmentTree() {
  return request<Department[]>({
    url: '/api/v1/department',
    method: 'get',
  });
}

/**
 * 添加部门
 * @param {Department} data 部门数据
 * @returns {Promise<null>}
 */
export function addDepartment(data: Department) {
  return request<null>({
    url: '/api/v1/department',
    method: 'post',
    data,
  });
}

/**
 * 更新部门
 * @param {Department} data 部门数据
 * @returns {Promise<null>}
 */
export function updateDepartment(data: Department) {
  return request<null>({
    url: `/api/v1/department/${data.id}`,
    method: 'put',
    data,
  });
}

/**
 * 删除部门
 * @param {number} id 部门ID
 * @returns {Promise<null>}
 */
export function deleteDepartment(id: number) {
  return request<null>({
    url: `/api/v1/department/${id}`,
    method: 'delete',
  });
}
