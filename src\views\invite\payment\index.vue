<template>
  <div class="payment-container">
    <el-card class="payment-card">
      <!-- 统计卡片 -->
      <el-row
        :gutter="20"
        class="stats-container"
      >
        <el-col :span="8">
          <el-card shadow="hover">
            <template #header>
              <div class="card-header">
                <span>累计金额</span>
              </div>
            </template>
            <div class="stat-value">
              ¥ {{ (statsData.totalAmount / 100)?.toFixed(2) || '0.00' }}
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card shadow="hover">
            <template #header>
              <div class="card-header">
                <span>累计佣金</span>
              </div>
            </template>
            <div class="stat-value">
              ¥ {{ (statsData.totalCommission / 100)?.toFixed(2) || '0.00' }}
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card shadow="hover">
            <template #header>
              <div class="card-header">
                <span>已结算订单数量</span>
              </div>
            </template>
            <div class="stat-value">{{ statsData.settledCount || 0 }} 笔</div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 搜索部分 -->
      <div class="filter-container">
        <el-form
          :inline="true"
          :model="searchForm"
          class="demo-form-inline"
        >
          <el-form-item label="支付状态">
            <div style="width: 180px">
              <el-select
                v-model="searchForm.status"
                placeholder="全部"
                clearable
              >
                <el-option
                  v-for="status in statusOptions"
                  :key="status.value"
                  :label="status.label"
                  :value="status.value"
                />
              </el-select>
            </div>
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              @click="handleSearch"
            >
              搜索
            </el-button>
            <el-button @click="resetSearchForm">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 数据表格 -->
      <el-table
        v-loading="tableLoading"
        :data="tableData"
        border
        stripe
        style="width: 100%"
      >
        <el-table-column
          prop="id"
          label="ID"
          width="80"
          align="center"
        />
        <el-table-column
          prop="OutTradeNo"
          label="订单号"
          width="200"
        />
        <el-table-column
          prop="PackageName"
          label="套餐名称"
          width="180"
        />
        <el-table-column
          prop="PackageDay"
          label="套餐天数"
          width="120"
        />
        <el-table-column
          prop="Total"
          label="订单金额"
          width="120"
        >
          <template #default="scope">
            <span>¥ {{ (scope.row.Total / 100).toFixed(2) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="PayTotal"
          label="实际支付金额"
          width="120"
        >
          <template #default="scope">
            <span>¥ {{ (scope.row.PayTotal / 100).toFixed(2) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="Status"
          label="支付状态"
          width="100"
        >
          <template #default="scope">
            <el-tag
              :type="
                scope.row.Status === 1 ? 'warning' : scope.row.Status === 2 ? 'success' : 'danger'
              "
            >
              {{ formatStatus(scope.row.Status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="SettlementStatus"
          label="结算状态"
          width="100"
        >
          <template #default="scope">
            <el-tag :type="scope.row.SettlementStatus === 1 ? 'success' : 'warning'">
              {{ scope.row.SettlementStatus === 1 ? '已结算' : '未结算' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="User.nick_name"
          label="用户账号"
          width="140"
        />
        <el-table-column
          prop="PaymentMethod"
          label="支付方式"
          width="100"
        />
        <el-table-column
          prop="PaymentDate"
          label="支付时间"
          width="180"
        >
          <template #default="scope">
            <span>{{ formatDate(scope.row.PaymentDate) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="CreatedAt"
          label="创建时间"
          width="180"
        >
          <template #default="scope">
            <span>{{ formatDate(scope.row.CreatedAt) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="UpdatedAt"
          label="更新时间"
          width="180"
        >
          <template #default="scope">
            <span>{{ formatDate(scope.row.UpdatedAt) }}</span>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { getSharePaymentList, SharePaymentDetail, SharePaymentStats } from '@/api/invite/payment';
import { formatDate } from '@/utils/tools';

// 支付状态选项
const statusOptions = [
  { value: 1, label: '未支付' },
  { value: 2, label: '已支付' },
  { value: 3, label: '退款' },
];

// 搜索表单
const searchForm = reactive({
  status: null,
});

// 支付状态格式化
const formatStatus = (status: number) => {
  switch (status) {
    case 1:
      return '未支付';
    case 2:
      return '已支付';
    case 3:
      return '退款';
    default:
      return '';
  }
};

// 表格数据
const tableData = ref<SharePaymentDetail[]>([]);
const tableLoading = ref(false);

// 分页数据
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0,
});

// 统计数据
const statsData = reactive<SharePaymentStats>({
  totalAmount: 0,
  totalCommission: 0,
  settledCount: 0,
});

// 获取支付明细列表
const getPaymentListData = async () => {
  tableLoading.value = true;

  try {
    const params = {
      page: pagination.page,
      page_size: pagination.pageSize,
      ...searchForm,
    };

    const res = await getSharePaymentList(params);

    tableData.value = res.list.list;
    pagination.total = res.total;

    // 更新统计数据
    statsData.totalAmount = res.list.order_total || 0;
    statsData.totalCommission = res.list.brokerage || 0;
    statsData.settledCount = res.list.settlement_num || 0;
  } catch (error) {
    console.error('获取邀请支付明细列表失败', error);
    ElMessage.error('获取邀请支付明细列表失败');
  } finally {
    tableLoading.value = false;
  }
};

// 分页大小改变事件
const handleSizeChange = (newSize: number) => {
  pagination.pageSize = newSize;
  getPaymentListData();
};

// 页码改变事件
const handleCurrentChange = (newPage: number) => {
  pagination.page = newPage;
  getPaymentListData();
};

// 搜索操作
const handleSearch = () => {
  pagination.page = 1;
  getPaymentListData();
};

// 重置搜索表单
const resetSearchForm = () => {
  searchForm.status = null;
  handleSearch();
};

// 组件挂载时获取数据
onMounted(() => {
  getPaymentListData();
});
</script>

<style scoped>
.payment-container {
  padding: 20px;
}

.payment-card {
  margin-bottom: 20px;
}

.stats-container {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  text-align: center;
}

.filter-container {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
