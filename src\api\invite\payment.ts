import request from '@/utils/request';

// 邀请支付明细类型定义
export interface SharePaymentDetail {
  id: number;
  Uid: number;
  package_id: number;
  PackageName: string;
  PackageDay: number;
  OutTradeNo: string;
  IsSample: string;
  Status: number;
  SettlementStatus: number;
  Total: number;
  PayTotal: number;
  PaymentMethod: string;
  PaymentDate: string | null;
  CreatedAt: string;
  UpdatedAt: string;
}

// 邀请支付统计类型定义
export interface SharePaymentStats {
  totalAmount: number; // 累计金额
  totalCommission: number; // 累计佣金
  settledCount: number; // 已结算订单数量
}

// 邀请支付明细响应类型
export interface SharePaymentResponse {
  list: {
    list: SharePaymentDetail[];
    order_total: number; // 累计订单金额
    brokerage: number; // 累计佣金
    settlement_num: number; // 已结算订单数量
  };
  total: number;
}

/**
 * 获取邀请支付明细列表
 * @param params 查询参数
 * @returns 邀请支付明细列表数据
 */
export function getSharePaymentList(params: { page: number; page_size: number }) {
  return request<SharePaymentResponse>({
    url: '/api/v1/order/share',
    method: 'get',
    params,
  });
}
