<template>
  <div class="role-container">
    <el-card class="box-card">
      <!-- 操作按钮区域 -->
      <el-row
        :gutter="10"
        class="mb8"
      >
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="Plus"
            @click="handleAdd"
          >
            新增
          </el-button>
        </el-col>
      </el-row>

      <!-- 表格展示区域 -->
      <el-table
        v-loading="loading"
        :data="roleList"
        border
      >
        <el-table-column
          type="index"
          label="序号"
          width="50"
          align="center"
        />
        <el-table-column
          prop="name"
          label="角色名称"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          prop="des"
          label="角色描述"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          prop="CreatedAt"
          label="创建时间"
          width="180"
        >
          <template #default="scope">
            {{ formatDate(scope.row.CreatedAt) }}
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          width="250"
          align="center"
        >
          <template #default="scope">
            <el-button
              type="primary"
              link
              icon="Edit"
              @click="handleUpdate(scope.row)"
            >
              修改
            </el-button>
            <el-button
              type="success"
              link
              icon="Menu"
              @click="handleAssignMenu(scope.row)"
            >
              分配菜单
            </el-button>
            <el-button
              type="danger"
              link
              icon="Delete"
              @click="handleDelete(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 添加或修改角色弹窗 -->
    <el-dialog
      v-model="dialog.visible"
      :title="dialog.title"
      width="500px"
      append-to-body
    >
      <el-form
        ref="roleForm"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item
          label="角色名称"
          prop="name"
        >
          <el-input
            v-model="form.name"
            placeholder="请输入角色名称"
          />
        </el-form-item>
        <el-form-item
          label="角色描述"
          prop="des"
        >
          <el-input
            v-model="form.des"
            type="textarea"
            placeholder="请输入角色描述"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button
            type="primary"
            @click="submitForm"
          >
            确 定
          </el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 分配菜单弹窗 -->
    <el-dialog
      v-model="menuDialog.visible"
      title="分配菜单"
      width="500px"
      append-to-body
    >
      <el-tree
        ref="menuTreeRef"
        :data="menuOptions"
        show-checkbox
        node-key="id"
        :props="{ label: 'name', children: 'children' }"
        default-expand-all
      />
      <template #footer>
        <div class="dialog-footer">
          <el-button
            type="primary"
            @click="submitMenuAssign"
          >
            确 定
          </el-button>
          <el-button @click="cancelMenuAssign">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, reactive, toRefs } from 'vue';
import { ElMessage, ElMessageBox, ElTree } from 'element-plus';
import type { FormInstance, FormRules } from 'element-plus';
import {
  getRoleList,
  addRole,
  updateRole,
  deleteRole,
  assignRoleMenu,
  getRoleMenu,
  RoleForm,
  RoleInfo,
  AssignMenuParams,
} from '@/api/system/role';
import { getMenuList, MenuItem } from '@/api/system/menu';
import { formatDate } from '@/utils/tools';

// 初始化数据
const state = reactive({
  // 遮罩层
  loading: false,
  // 角色表格数据
  roleList: [] as RoleInfo[],
  // 角色弹窗
  dialog: {
    title: '',
    visible: false,
  },
  // 菜单弹窗
  menuDialog: {
    visible: false,
    roleId: '',
  },
  // 表单参数
  form: {
    name: '',
    des: '',
  } as RoleForm,
  // 表单校验
  rules: {
    name: [{ required: true, message: '角色名称不能为空', trigger: 'blur' }],
  } as FormRules,
});

const { loading, roleList, dialog, menuDialog, form, rules } = toRefs(state);

// 菜单树选项
const menuOptions = ref<MenuItem[]>([]);
// 表单引用
const roleForm = ref<FormInstance>();
// 菜单树引用
const menuTreeRef = ref<InstanceType<typeof ElTree>>();

// 查询角色列表
const getList = async () => {
  state.loading = true;
  try {
    const res = await getRoleList();
    // 直接使用返回的数组
    if (Array.isArray(res)) {
      state.roleList = res;
    } else {
      // 数据格式异常，置空
      state.roleList = [];
      console.error('角色列表数据格式异常', res);
    }
  } catch (error) {
    state.roleList = [];
    console.error('获取角色列表出错', error);
  } finally {
    state.loading = false;
  }
};

// 获取菜单树
const getMenuTreeData = async () => {
  try {
    const res = await getMenuList();
    // 去掉id为0
    menuOptions.value = res.filter(item => item.id !== 0);
  } catch (error) {
    console.error('获取菜单列表失败', error);
  }
};

// 初始化表单数据
const resetForm = () => {
  state.form = {
    name: '',
    des: '',
  };
  if (roleForm.value) {
    roleForm.value.resetFields();
  }
};

// 新增按钮操作
const handleAdd = () => {
  resetForm();
  state.dialog = {
    title: '添加角色',
    visible: true,
  };
};

// 修改按钮操作
const handleUpdate = (row: RoleInfo) => {
  resetForm();
  // 直接使用表格行数据
  state.form = {
    id: row.id,
    name: row.name,
    des: row.des || '',
  };
  state.dialog = {
    title: '修改角色',
    visible: true,
  };
};

// 分配菜单按钮操作
const handleAssignMenu = async (row: RoleInfo) => {
  try {
    // 先加载菜单树数据
    await getMenuTreeData();

    // 设置当前角色ID
    state.menuDialog.roleId = row.id;

    // 获取角色已有菜单权限
    const res = await getRoleMenu(row.id);
    console.log('res', res);

    // 先显示对话框，确保菜单树组件被渲染
    state.menuDialog.visible = true;

    // 存储菜单IDs，稍后在对话框显示后设置选中状态
    const menuIds = res.menu_ids;

    // 使用setTimeout确保对话框中的组件已完全渲染
    setTimeout(() => {
      if (menuTreeRef.value) {
        const checkedKeys = menuIds ? menuIds.split(',').map(id => Number(id)) : [];
        menuTreeRef.value.setCheckedKeys(checkedKeys);
      } else {
        console.warn('无法设置选中状态，menuTreeRef或menuIds无效', {
          menuTreeRef: menuTreeRef.value,
          menuIds,
        });
      }
    }, 300); // 给予足够的时间让组件渲染
  } catch (error) {
    console.error('获取角色菜单失败:', error);
    ElMessage.error('获取角色菜单失败');
  }
};

// 提交角色表单
const submitForm = async () => {
  if (!roleForm.value) return;

  await roleForm.value.validate(async valid => {
    if (valid) {
      try {
        if (state.form.id) {
          // 修改
          await updateRole(state.form.id, state.form);
        } else {
          // 新增
          await addRole(state.form);
        }
        ElMessage.success(state.form.id ? '修改成功' : '新增成功');
        state.dialog.visible = false;
        getList();
      } catch (error) {
        console.error('提交失败', error);
        ElMessage.error(state.form.id ? '修改失败' : '新增失败');
      }
    }
  });
};

// 提交菜单分配
const submitMenuAssign = async () => {
  try {
    // 获取选中的菜单ID
    const checkedKeys = menuTreeRef.value?.getCheckedKeys(false) as number[];
    if (!checkedKeys || !state.menuDialog.roleId) {
      ElMessage.warning('请选择菜单或角色ID无效');
      return;
    }

    // 将菜单ID转为字符串
    const menuIds = checkedKeys.join(',');

    const params: AssignMenuParams = {
      role_id: state.menuDialog.roleId,
      menu_ids: menuIds,
    };

    await assignRoleMenu(params);

    ElMessage.success('分配菜单成功');
    state.menuDialog.visible = false;
  } catch (error) {
    console.error('分配菜单失败', error);
    ElMessage.error('分配菜单失败');
  }
};

// 取消角色表单
const cancel = () => {
  state.dialog.visible = false;
  resetForm();
};

// 取消菜单分配
const cancelMenuAssign = () => {
  state.menuDialog.visible = false;
};

// 删除按钮操作
const handleDelete = (row: RoleInfo) => {
  ElMessageBox.confirm(`确认删除角色"${row.name}"吗？`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      try {
        await deleteRole(row.id);
        ElMessage.success('删除成功');
        getList();
      } catch (error) {
        console.error('删除失败', error);
        ElMessage.error('删除失败');
      }
    })
    .catch(() => {});
};

// 页面加载时
onMounted(() => {
  getList();
});
</script>

<style lang="scss" scoped>
.role-container {
  padding: 20px;
}
.mb8 {
  margin-bottom: 8px;
}
</style>
