import { defineConfig, loadEnv } from 'vite';
import vue from '@vitejs/plugin-vue';
import { resolve } from 'path';
import eslintPlugin from 'vite-plugin-eslint';

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd());
  const isProd = mode === 'production';

  return {
    // 添加base配置，使用相对路径
    base: './',
    plugins: [
      vue(),
      eslintPlugin({
        include: [
          'src/**/*.js',
          'src/**/*.ts',
          'src/**/*.vue',
          'src/*.js',
          'src/*.ts',
          'src/*.vue',
        ],
        cache: false,
        failOnError: false,
      }),
    ],
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src'),
      },
    },
    // 添加esbuild配置，在生产环境下移除console.log
    esbuild: {
      drop: isProd ? ['console'] : [],
    },
    server: {
      port: parseInt(env.VITE_APP_PORT || '3000'),
      // host: '**************',
      open: true,
      cors: true,
      proxy: {
        '/dev-api': {
          target: 'http://**************:9023',
          rewrite: path => path.replace(/^\/dev-api/, ''),
          changeOrigin: true,
        },
      },
    },
  };
});
