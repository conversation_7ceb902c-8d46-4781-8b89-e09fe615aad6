<template>
  <div class="position-container">
    <div class="position-header">
      <el-card class="box-card">
        <template #header>
          <div class="card-header">
            <span>岗位管理</span>
          </div>
        </template>

        <!-- 操作按钮组 -->
        <el-row
          :gutter="10"
          class="mb-8"
        >
          <el-col :span="1.5">
            <el-button
              type="primary"
              plain
              :icon="Plus"
              @click="handleAdd"
            >
              新增
            </el-button>
          </el-col>
        </el-row>

        <!-- 岗位表格 -->
        <el-table
          v-loading="loading"
          :data="positionList"
          stripe
          border
          style="width: 100%"
        >
          <el-table-column
            prop="id"
            label="序号"
            width="60"
            align="center"
          />
          <el-table-column
            prop="name"
            label="岗位名称"
            min-width="300"
          />
          <el-table-column
            label="操作"
            width="200"
            align="center"
          >
            <template #default="{ row }">
              <el-button
                type="primary"
                link
                :icon="Edit"
                @click="handleEdit(row)"
              >
                编辑
              </el-button>
              <el-button
                type="danger"
                link
                :icon="Delete"
                @click="handleDelete(row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

    <!-- 岗位对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'add' ? '新增岗位' : '编辑岗位'"
      width="500px"
      destroy-on-close
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="formRules"
        label-position="right"
        label-width="100px"
      >
        <el-form-item
          label="岗位名称"
          prop="name"
        >
          <el-input
            v-model="form.name"
            placeholder="请输入岗位名称"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="handleSubmit"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
/**
 * 岗位管理组件
 * 提供岗位的列表展示、新增、编辑、删除等功能
 * @component PositionManagement
 */
import { ref, reactive, onMounted } from 'vue';
import { Plus, Edit, Delete } from '@element-plus/icons-vue';
import type { FormInstance, FormRules } from 'element-plus';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  Position,
  getAllPositions,
  addPosition,
  updatePosition,
  deletePosition,
} from '@/api/system/position';

// 岗位列表
const positionList = ref<Position[]>([]);
// 加载状态
const loading = ref<boolean>(false);
// 对话框可见性
const dialogVisible = ref<boolean>(false);
// 对话框类型：add-新增，edit-编辑
const dialogType = ref<'add' | 'edit'>('add');
// 表单引用
const formRef = ref<FormInstance>();

// 表单数据
const form = reactive<Position>({
  id: undefined,
  name: '',
});

// 表单校验规则
const formRules = reactive<FormRules>({
  name: [
    { required: true, message: '请输入岗位名称', trigger: 'blur' },
    { min: 2, max: 50, message: '岗位名称长度在2-50个字符之间', trigger: 'blur' },
  ],
});

// 初始化
onMounted(() => {
  getPositionData();
});

// 获取岗位数据
const getPositionData = async () => {
  loading.value = true;
  try {
    const res = await getAllPositions();
    positionList.value = res as Position[];
  } catch (error) {
    console.error('获取岗位数据出错:', error);
    ElMessage.error('获取岗位数据出错');
  } finally {
    loading.value = false;
  }
};

// 处理新增岗位
const handleAdd = () => {
  dialogType.value = 'add';
  resetForm();
  dialogVisible.value = true;
};

// 处理编辑岗位
const handleEdit = (row: Position) => {
  dialogType.value = 'edit';
  resetForm();
  // 填充表单数据
  Object.assign(form, row);
  dialogVisible.value = true;
};

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields();
  }
  form.id = undefined;
  form.name = '';
};

// 处理表单提交
const handleSubmit = async () => {
  if (!formRef.value) return;

  await formRef.value.validate(async valid => {
    if (valid) {
      try {
        if (dialogType.value === 'add') {
          // 新增岗位
          await addPosition(form);
          ElMessage.success('新增岗位成功');
        } else {
          // 编辑岗位
          await updatePosition(form);
          ElMessage.success('更新岗位成功');
        }
        dialogVisible.value = false;
        getPositionData();
      } catch (error) {
        console.error('提交岗位数据出错:', error);
        ElMessage.error('提交岗位数据出错');
      }
    }
  });
};

// 处理删除岗位
const handleDelete = (row: Position) => {
  ElMessageBox.confirm(`确定要删除岗位 "${row.name}" 吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      try {
        await deletePosition(row.id as number);
        ElMessage.success('删除岗位成功');
        getPositionData();
      } catch (error) {
        console.error('删除岗位出错:', error);
        ElMessage.error('删除岗位出错');
      }
    })
    .catch(() => {
      // 取消删除
    });
};
</script>

<style scoped>
.position-container {
  padding: 10px;
}

.position-header {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.mb-8 {
  margin-bottom: 8px;
}
</style>
