import { defineStore } from 'pinia';
import { ref } from 'vue';
import { RouteRecordRaw } from 'vue-router';
import { getToken, getUserInfo, removeToken, removeUserInfo } from '@/utils/auth';
import { getRouters } from '@/api/menu';
import { type MenuItem } from '@/api/system/menu';
import router from '@/router';
// 匹配views里面所有的.vue文件
const modules = import.meta.glob('./../views/**/*.vue');

export const loadView = view => {
  let res;
  for (const path in modules) {
    const dir = path.split('views/')[1].split('.vue')[0];
    // 处理可能带有/views前缀的路径
    const normalizedView = view.replace(/^\/views\//, '');
    if (dir === normalizedView) {
      // console.log(normalizedView);

      res = () => modules[path]();
    }
  }

  return res;
};

// 获取侧边栏的路由菜单
const getSidebarRoutes = (routes: MenuItem[]) => {
  const sidebarRoutes = routes.filter(route => route.is_show === 1);
  return sidebarRoutes.map(route => {
    // console.log(route);
    return {
      path: route.path,
      name: route.name,
      component: loadView(route.group_path),
      meta: {
        title: route.name,
        icon: route.icon || 'Document',
      },
      children: route.children ? getSidebarRoutes(route.children) : [],
    };
  });
};

// 获取侧边栏的路由菜单
const getAllRoutes = (routes: MenuItem[]) => {
  return routes.map(route => {
    // console.log(route);
    return {
      path: route.path,
      name: route.name,
      component: loadView(route.group_path),
      meta: {
        title: route.name,
        icon: route.icon || 'Document',
      },
      children: route.children ? getSidebarRoutes(route.children) : [],
    };
  });
};

export const useUserStore = defineStore('user', () => {
  const token = ref<string>(getToken() || '');
  const userInfo = ref<any>({});
  const dynamicRoutes = ref<RouteRecordRaw[]>([]);
  const allRoutes = ref<RouteRecordRaw[]>([]);
  const sidebarRoutes = ref<RouteRecordRaw[]>([]);

  // 获取用户菜单/api/v1/menu/my
  const getUserMenu = () => {
    return new Promise(resolve => {
      getRouters().then(res => {
        // console.log('获取用户菜单:', res);
        setSidebarRoutes(getSidebarRoutes(res as MenuItem[]));
        addRoutes(getAllRoutes(res as MenuItem[]));
        setAllRoutes(getAllRoutes(res as MenuItem[]));
        resolve(getAllRoutes(res as MenuItem[]));
      });
    });
  };

  // 设置所有的路由
  const setAllRoutes = (routes: RouteRecordRaw[]) => {
    allRoutes.value = routes;
  };

  const setSidebarRoutes = (routes: RouteRecordRaw[]) => {
    console.log('设置侧边栏路由:', routes);
    sidebarRoutes.value = routes;
  };

  // 添加路由
  const addRoutes = (routes: RouteRecordRaw[]) => {
    routes.forEach(route => {
      router.addRoute('Layout', route);
    });
  };

  // 初始化用户信息
  const initUserInfo = () => {
    const storedUserInfo = getUserInfo();
    console.log('从localStorage获取的用户信息:', storedUserInfo);
    try {
      if (storedUserInfo) {
        const parsedInfo = JSON.parse(storedUserInfo);
        userInfo.value = parsedInfo;
        console.log('初始化用户信息成功:', userInfo.value);

        // 加载动态路由
        // loadDynamicRoutes()
      } else {
        console.log('localStorage中没有用户信息');
        userInfo.value = {};
      }
    } catch (error) {
      console.error('解析用户信息失败:', error);
      userInfo.value = {};
    }
  };

  // 设置用户信息
  const setUserInfo = (info: any) => {
    if (!info) {
      console.warn('尝试设置空的用户信息');
      return;
    }

    userInfo.value = info;
    localStorage.setItem('userInfo', JSON.stringify(info));
    console.log('Store设置用户信息:', userInfo.value);
  };

  // 加载动态路由
  // const loadDynamicRoutes = () => {
  //   // 确保已登录且有用户信息
  //   if (!token.value || !userInfo.value) {
  //     console.warn('无法加载动态路由：用户未登录')
  //     return
  //   }

  //   const routes: RouteRecordRaw[] = []

  //   // 不再基于权限添加路由，所有动态路由对所有用户可见
  //   // 可以在这里添加动态路由

  //   // 保存动态路由
  //   dynamicRoutes.value = routes

  //   // 添加到路由中
  //   addRoutesToRouter(routes)

  //   console.log('动态路由加载完成:', routes)
  // }

  // 将动态路由添加到路由实例
  // const addRoutesToRouter = (routes: RouteRecordRaw[]) => {
  //   // 防止重复添加
  //   removeExistingDynamicRoutes()

  //   // 将每个路由添加为Layout的子路由
  //   routes.forEach(route => {
  //     router.addRoute('Layout', route)
  //   })
  // }

  // 简单的实现，防止其他地方调用出错
  // const removeExistingDynamicRoutes = () => {
  //   console.log('移除动态路由')
  //   // 这里不做任何操作，因为动态路由功能已禁用
  // }

  // 清除用户信息
  const clearUserInfo = () => {
    // 清除动态路由
    // removeExistingDynamicRoutes() - 函数已注释掉，不应该调用
    dynamicRoutes.value = [];

    // 清除 store 中的数据
    token.value = '';
    userInfo.value = {};

    // 同步清除本地存储
    removeToken();
    removeUserInfo();
    localStorage.removeItem('permissions');
    localStorage.removeItem('routes');
    localStorage.removeItem('networkErrors');

    // 清除会话存储
    sessionStorage.clear();

    // 清除所有相关的 cookies
    const cookies = document.cookie.split(';');
    for (let i = 0; i < cookies.length; i++) {
      const cookie = cookies[i];
      const eqPos = cookie.indexOf('=');
      const name = eqPos > -1 ? cookie.substr(0, eqPos).trim() : cookie.trim();
      document.cookie = name + '=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/';
    }

    console.log('用户信息清除成功');
  };

  // 初始化用户信息
  initUserInfo();

  return {
    token,
    userInfo,
    dynamicRoutes,
    setUserInfo,
    clearUserInfo,
    initUserInfo, // 导出初始化方法，以便需要时可以手动调用
    getUserMenu,
    allRoutes,
    sidebarRoutes, // 添加侧边栏路由到返回对象中
  };
});
