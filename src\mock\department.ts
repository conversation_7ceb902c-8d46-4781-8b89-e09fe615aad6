import Mock from 'mockjs';
import { Department } from '@/api/system/department';

// 生成模拟的部门数据
const departments: Department[] = [
  {
    id: 0,
    name: '总公司',
    parent_id: null,
    children: [
      {
        id: 2,
        name: '研发部',
        parent_id: 1,
        children: [
          {
            id: 5,
            name: '前端组',
            parent_id: 2,
          },
          {
            id: 6,
            name: '后端组',
            parent_id: 2,
          },
        ],
      },
      {
        id: 3,
        name: '市场部',
        parent_id: 1,
      },
      {
        id: 4,
        name: '行政部',
        parent_id: 1,
        children: [
          {
            id: 7,
            name: '人事组',
            parent_id: 4,
          },
          {
            id: 8,
            name: '财务组',
            parent_id: 4,
          },
        ],
      },
    ],
  },
];

// 模拟ID计数器，用于新增部门
let idCounter = 10;

// 获取部门树形结构
Mock.mock(/\/department\/tree/, 'get', (): Department[] => {
  return JSON.parse(JSON.stringify(departments));
});

// 获取部门列表（扁平结构）
Mock.mock(/\/department\/list/, 'get', (): Department[] => {
  // 将嵌套结构扁平化
  const flattenDepartments = (deps: Department[], result: Department[] = []): Department[] => {
    deps.forEach(dep => {
      const { children, ...rest } = dep;
      result.push(rest as Department);
      if (children && children.length > 0) {
        flattenDepartments(children, result);
      }
    });
    return result;
  };

  return {
    code: 0,
    msg: '获取成功',
    data: flattenDepartments(JSON.parse(JSON.stringify(departments))),
  };
});

// 添加部门
Mock.mock(/\/department/, 'post', (options: any): null => {
  const body = JSON.parse(options.body);
  const newDepartment: Department = {
    ...body,
    id: idCounter++,
  };

  // 添加到父部门的children中
  const addToDepartment = (deps: Department[], parentId: number, newDep: Department): boolean => {
    for (const dep of deps) {
      if (dep.id === parentId) {
        if (!dep.children) {
          dep.children = [];
        }
        dep.children.push(newDep);
        return true;
      }
      if (dep.children && dep.children.length > 0) {
        if (addToDepartment(dep.children, parentId, newDep)) {
          return true;
        }
      }
    }
    return false;
  };

  // 如果父ID为0，则添加到根节点
  if (body.parent_id === 0) {
    departments.push(newDepartment);
  } else {
    addToDepartment(departments, body.parent_id, newDepartment);
  }

  return {
    code: 0,
    msg: '添加成功',
    data: null,
  };
});

// 更新部门
Mock.mock(/\/department\/\d+/, 'put', (options: any): null => {
  const body = JSON.parse(options.body);

  // 递归查找并更新部门信息
  const updateDepartment = (
    deps: Department[],
    id: number,
    updateData: Partial<Department>
  ): boolean => {
    for (let i = 0; i < deps.length; i++) {
      if (deps[i].id === id) {
        deps[i] = { ...deps[i], ...updateData };
        return true;
      }
      if (deps[i].children && deps[i].children.length > 0) {
        if (updateDepartment(deps[i].children, id, updateData)) {
          return true;
        }
      }
    }
    return false;
  };

  updateDepartment(departments, body.id, body);

  return {
    code: 0,
    msg: '更新成功',
    data: null,
  };
});

// 删除部门
Mock.mock(/\/department\/\d+/, 'delete', (options: any): null => {
  const url = options.url;
  const id = parseInt(url.substring(url.lastIndexOf('/') + 1));

  // 递归查找并删除部门
  const deleteDepartment = (deps: Department[], id: number): boolean => {
    for (let i = 0; i < deps.length; i++) {
      if (deps[i].id === id) {
        deps.splice(i, 1);
        return true;
      }
      if (deps[i].children && deps[i].children.length > 0) {
        if (deleteDepartment(deps[i].children, id)) {
          return true;
        }
      }
    }
    return false;
  };

  deleteDepartment(departments, id);

  return {
    code: 0,
    msg: '删除成功',
    data: null,
  };
});

export const setupDepartmentMock = () => {
  console.log('部门管理模拟数据已设置');
};
