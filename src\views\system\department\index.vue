<template>
  <div class="department-container">
    <!-- 部门树形组件 -->
    <div class="department-tree-container">
      <el-tree
        v-loading="loading"
        :data="departmentList"
        node-key="id"
        default-expand-all
        :props="{ label: 'name', children: 'children' }"
        highlight-current
        @node-click="handleNodeClick"
      >
        <template #default="{ node, data }">
          <div class="custom-tree-node">
            <span>{{ node.label }}</span>
            <div class="node-actions">
              <el-button
                type="primary"
                link
                :icon="CirclePlus"
                title="添加子部门"
                @click.stop="handleAddDepartment(data)"
              />
              <el-button
                v-if="data.id !== 0"
                type="primary"
                link
                :icon="Edit"
                title="编辑部门"
                @click.stop="handleEdit(data)"
              />
              <el-button
                v-if="data.id !== 0"
                type="danger"
                link
                :icon="Delete"
                title="删除部门"
                @click.stop="handleDelete(data)"
              />
            </div>
          </div>
        </template>
      </el-tree>
    </div>

    <!-- 部门表单对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'add' ? '新增部门' : '编辑部门'"
      width="500px"
    >
      <el-form
        ref="formRef"
        :model="form"
        label-width="100px"
        :rules="formRules"
      >
        <el-form-item
          label="上级部门"
          prop="parent_id"
        >
          <el-tree-select
            v-model="form.parent_id"
            :data="treeSelectData"
            placeholder="请选择上级部门"
            value-key="id"
            check-strictly
            :render-after-expand="false"
            :props="{
              value: 'id',
              label: 'name',
              children: 'children',
            }"
          />
        </el-form-item>
        <el-form-item
          label="部门名称"
          prop="name"
        >
          <el-input v-model="form.name" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
        >
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
/**
 * 部门管理组件
 * 提供部门的树形展示、新增、编辑、删除等功能
 * 支持多级部门结构
 * @component DepartmentManagement
 */
import { Edit, Delete, CirclePlus } from '@element-plus/icons-vue';
import { reactive, ref, onMounted } from 'vue';
import type { FormRules, FormInstance } from 'element-plus';
import {
  Department,
  getDepartmentTree,
  addDepartment,
  updateDepartment,
  deleteDepartment,
} from '@/api/system/department';
import { ElMessage, ElMessageBox } from 'element-plus';

// 定义事件
const emit = defineEmits(['filter-department']);

// 部门列表数据
const departmentList = ref<Department[]>([]);
// 树选择数据
const treeSelectData = ref<Department[]>([]);
// 加载状态
const loading = ref(false);
// 对话框可见性
const dialogVisible = ref(false);
// 对话框类型：add-新增，edit-编辑
const dialogType = ref<'add' | 'edit'>('add');
// 表单引用
const formRef = ref<FormInstance>();

// 表单数据
const form = reactive<Department>({
  id: undefined,
  name: '',
  parent_id: null,
});

// 表单校验规则
const formRules = reactive<FormRules>({
  name: [{ required: true, message: '请输入部门名称', trigger: 'blur' }],
  parent_id: [{ required: true, message: '请选择上级部门', trigger: 'change' }],
});

// 初始化
onMounted(() => {
  getDepartmentData();
});

// 获取部门数据
const getDepartmentData = async () => {
  loading.value = true;
  try {
    const data = await getDepartmentTree();
    console.log(data);
    departmentList.value = data;
    // 构建树选择数据
    treeSelectData.value = data;
  } catch (error) {
    console.error('获取部门数据出错:', error);
    ElMessage.error('获取部门数据出错');
  } finally {
    loading.value = false;
  }
};

// 处理节点点击事件
const handleNodeClick = (data: Department) => {
  // 触发筛选事件，将部门ID传递给父组件
  emit('filter-department', data.id);
};

// 处理新增部门
const handleAddDepartment = (row: Department | null) => {
  dialogType.value = 'add';
  resetForm();

  if (row) {
    // 新增子部门，设置父部门ID
    form.parent_id = row.id as number;
  } else {
    // 新增顶级部门
    form.parent_id = null;
  }

  dialogVisible.value = true;
};

// 处理编辑部门
const handleEdit = (row: Department) => {
  dialogType.value = 'edit';
  resetForm();

  // 填充表单数据
  form.id = row.id;
  form.name = row.name;
  form.parent_id = row.parent_id;

  dialogVisible.value = true;
};

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields();
  }

  form.id = undefined;
  form.name = '';
  form.parent_id = null;
};

// 处理表单提交
const handleSubmit = () => {
  if (!formRef.value) return;

  formRef.value.validate(valid => {
    if (valid) {
      try {
        if (dialogType.value === 'add') {
          console.log(form);

          // 新增部门
          addDepartment(form)
            .then(() => {
              ElMessage.success('新增部门成功');
              dialogVisible.value = false;
              getDepartmentData();
            })
            .catch(error => {
              console.error('新增部门失败:', error);
            });
        } else {
          // 编辑部门
          updateDepartment(form)
            .then(() => {
              ElMessage.success('更新部门成功');
              dialogVisible.value = false;
              getDepartmentData();
            })
            .catch(error => {
              console.error('更新部门失败:', error);
            });
        }
      } catch (error) {
        console.error('提交部门数据出错:', error);
        ElMessage.error('提交部门数据出错');
      }
    }
  });
};

// 处理删除部门
const handleDelete = (row: Department) => {
  // 判断是否为顶级部门（第一层级的部门）
  if (!row.parent_id || row.parent_id === 0) {
    ElMessage.warning('顶级部门不能删除');
    return;
  }

  // 有子部门时不允许删除
  if (row.children && row.children.length > 0) {
    ElMessage.warning('该部门下有子部门，不能删除');
    return;
  }

  ElMessageBox.confirm('确认删除该部门吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(() => {
      deleteDepartment(row.id as number)
        .then(() => {
          ElMessage.success('删除部门成功');
          getDepartmentData();
        })
        .catch(error => {
          console.error('删除部门失败:', error);
        });
    })
    .catch(() => {
      // 取消删除
    });
};
</script>

<style scoped>
.department-container {
  padding: 20px;
  padding-top: 0;
}
.department-header {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 20px;
}
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
  width: 100%;
}

.node-actions {
  margin-left: 20px;
}
</style>
