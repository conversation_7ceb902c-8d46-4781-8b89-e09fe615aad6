import { ApiResponse } from '@/api/types';

/**
 * 返回成功响应
 * @param data 返回的数据
 * @param msg 成功消息
 */
export function resultSuccess<T>(data: T, msg = '操作成功'): ApiResponse<T> {
  return {
    code: 0,
    data,
    msg,
  };
}

/**
 * 返回错误响应
 * @param msg 错误消息
 * @param code 错误码
 */
export function resultError(msg = '操作失败', code = 500): ApiResponse<null> {
  return {
    code,
    data: null,
    msg,
  };
}

/**
 * 返回分页数据
 * @param data 列表数据
 * @param total 总数
 * @param page 当前页码
 * @param pageSize 每页条数
 */
export function resultPageSuccess<T>(
  data: T[],
  total: number,
  page = 1,
  pageSize = 10
): ApiResponse<{ list: T[]; total: number; page: number; pageSize: number }> {
  return {
    code: 0,
    msg: '获取成功',
    data: {
      list: data,
      total,
      page,
      pageSize,
    },
  };
}

/**
 * 生成随机数字
 * @param min 最小值
 * @param max 最大值
 */
export function randomNumber(min: number, max: number): number {
  return Math.floor(Math.random() * (max - min + 1) + min);
}

/**
 * 深拷贝对象
 * @param obj 要拷贝的对象
 */
export function deepClone<T>(obj: T): T {
  return JSON.parse(JSON.stringify(obj));
} 