{"name": "my-admin", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --mode development", "dev:prod": "vite --mode production", "dev:lint": "vite --mode development", "lint": "eslint --ext .js,.ts,.vue src", "lint:fix": "eslint --ext .js,.ts,.vue src --fix", "lint:script": "node scripts/lint-fix.js", "check:ignores": "node scripts/check-eslint-ignore.js", "format": "prettier --write \"src/**/*.{js,ts,vue,scss,css}\"", "format:check": "prettier --check \"src/**/*.{js,ts,vue,scss,css}\"", "format:all": "node scripts/format-all.js", "test": "vite --mode test", "build:dev": "vite build --mode development", "build:test": "vite build --mode test", "build:prod": "vite build --mode production", "build": "vite build --mode production", "preview": "vite preview", "preview:test": "vite preview --mode test"}, "dependencies": {"@playwright/test": "^1.51.1", "axios": "^1.8.4", "crypto-js": "^4.2.0", "echarts": "^5.6.0", "element-plus": "^2.9.7", "pinia": "^3.0.2", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/mockjs": "^1.0.10", "@types/node": "^22.14.1", "@vitejs/plugin-vue": "^5.2.1", "@vue/eslint-config-prettier": "^10.2.0", "eslint": "^9.25.0", "eslint-plugin-prettier": "^5.2.6", "eslint-plugin-vue": "^10.0.0", "globals": "^16.0.0", "mockjs": "^1.1.0", "prettier": "^3.5.3", "sass": "^1.87.0", "typescript": "^5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.2.0", "vite-plugin-eslint": "^1.8.1"}}