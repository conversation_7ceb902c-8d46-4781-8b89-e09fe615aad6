// 格式化日期
export const formatDate = (dateString: string | null | undefined) => {
  if (!dateString || dateString.startsWith('0001-01-01')) return '暂无记录';
  try {
    const date = new Date(dateString);
    return date
      .toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false,
      })
      .replace(/\//g, '-');
  } catch (error) {
    console.error('日期格式化错误', error);
    return dateString;
  }
};

// 判断日期是否比当前日期小
export const isDateBeforeCurrent = (dateString: string) => {
  const date = new Date(dateString);
  const currentDate = new Date();
  return date < currentDate;
};
