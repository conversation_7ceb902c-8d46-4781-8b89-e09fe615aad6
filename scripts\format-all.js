#!/usr/bin/env node

/**
 * 综合的代码格式化和检查脚本
 * 同时运行ESLint和Prettier
 * 使用方法：node scripts/format-all.js
 */

import { execSync } from 'child_process';
import { join } from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const rootPath = join(__dirname, '..');

// 定义颜色代码
const colors = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  red: '\x1b[31m',
  blue: '\x1b[34m'
};

console.log(`${colors.blue}=== 开始代码格式化与检查 ===${colors.reset}`);

// 运行Prettier格式化
try {
  console.log(`\n${colors.yellow}1. 运行Prettier格式化...${colors.reset}`);
  execSync('prettier --write "src/**/*.{js,ts,vue,scss,css}"', { 
    cwd: rootPath, 
    stdio: 'inherit' 
  });
  console.log(`${colors.green}Prettier格式化完成！${colors.reset}`);
} catch (error) {
  console.error(`${colors.red}Prettier格式化出错：${error.message}${colors.reset}`);
  process.exit(1);
}

// 运行ESLint检查并尝试修复
try {
  console.log(`\n${colors.yellow}2. 运行ESLint检查...${colors.reset}`);
  execSync('eslint --ext .js,.ts,.vue src', { 
    cwd: rootPath, 
    stdio: 'inherit' 
  });
  console.log(`${colors.green}ESLint检查完成，未发现问题！${colors.reset}`);
} catch (error) {
  console.log(`\n${colors.yellow}发现ESLint问题，正在尝试自动修复...${colors.reset}`);
  try {
    execSync('eslint --ext .js,.ts,.vue src --fix', { 
      cwd: rootPath, 
      stdio: 'inherit' 
    });
    console.log(`${colors.green}ESLint自动修复完成！${colors.reset}`);
  } catch (fixError) {
    console.error(`${colors.red}ESLint自动修复失败，某些问题可能需要手动解决。${colors.reset}`);
    process.exit(1);
  }
}

console.log(`\n${colors.green}✓ 代码格式化与检查全部完成！${colors.reset}`); 