import request from '@/utils/request';

/**
 * 岗位接口类型定义
 */
export interface Position {
  id?: number;
  name: string;
}

/**
 * 获取所有岗位（不分页）
 * @returns 所有岗位列表
 */
export function getAllPositions() {
  return request({
    url: '/api/v1/post',
    method: 'get',
  });
}

/**
 * 新增岗位
 * @param data 岗位数据
 * @returns 操作结果
 */
export function addPosition(data: Position) {
  return request({
    url: '/api/v1/post',
    method: 'post',
    data,
  });
}

/**
 * 更新岗位
 * @param data 岗位数据
 * @returns 操作结果
 */
export function updatePosition(data: Position) {
  return request({
    url: `/api/v1/post/${data.id}`,
    method: 'put',
    data,
  });
}

/**
 * 删除岗位
 * @param id 岗位ID
 * @returns 操作结果
 */
export function deletePosition(id: number) {
  return request({
    url: `/api/v1/post/${id}`,
    method: 'delete',
  });
}
