<template>
  <div class="tags-view-container">
    <el-scrollbar
      ref="scrollbarRef"
      class="tags-view-wrapper"
    >
      <div
        v-for="tag in visitedViews"
        :key="tag.path"
        class="tags-view-item"
        :class="{ active: isActive(tag) }"
        @click="toTagView(tag)"
        @contextmenu.prevent="openMenu(tag, $event)"
      >
        <span>{{ tag.title }}</span>
        <el-icon
          class="el-icon-close"
          @click.stop="closeTagView(tag)"
        >
          <Close />
        </el-icon>
      </div>
    </el-scrollbar>

    <ul
      v-show="visible"
      class="contextmenu"
      :style="{ left: left + 'px', top: top + 'px' }"
    >
      <li @click="refreshSelectedTag(selectedTag)">刷新</li>
      <li @click="closeSelectedTag(selectedTag)">关闭</li>
      <li @click="closeOthersTags">关闭其他</li>
      <li @click="closeAllTags">关闭所有</li>
    </ul>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, nextTick } from 'vue';
import { useRoute, useRouter, RouteLocationNormalizedLoaded } from 'vue-router';
import { Close } from '@element-plus/icons-vue';

// 标签视图接口定义
interface TagView {
  path: string;
  title: string;
  name?: string;
  fullPath?: string;
}

const route = useRoute();
const router = useRouter();
const scrollbarRef = ref();

// 访问过的路由标签
const visitedViews = ref<TagView[]>([]);

// 初始标签（首页）
const defaultTag: TagView = {
  path: '/home',
  title: '首页',
  name: 'Home',
};

// 右键菜单相关
const visible = ref(false);
const left = ref(0);
const top = ref(0);
const selectedTag = ref<TagView>({} as TagView);

// 判断是否为活动标签
const isActive = (tag: TagView): boolean => {
  return tag.path === route.path;
};

// 跳转到标签对应的路由
const toTagView = (tag: TagView): void => {
  router.push(tag.path);
};

// 添加访问的视图
const addVisitedView = (route: RouteLocationNormalizedLoaded): void => {
  // 如果不存在标题则不添加
  if (!route.meta?.title) return;

  const { path, name, meta, fullPath } = route;

  // 检查标签是否已存在
  const isExist = visitedViews.value.some(v => v.path === path);
  if (!isExist) {
    visitedViews.value.push({
      path,
      title: meta.title as string,
      name: name as string | undefined,
      fullPath,
    });
  }
};

// 关闭标签
const closeTagView = (tag: TagView): void => {
  // 如果只剩下首页，则不能关闭
  if (visitedViews.value.length === 1 && tag.path === '/home') {
    return;
  }

  const index = visitedViews.value.findIndex(v => v.path === tag.path);
  if (index > -1) {
    visitedViews.value.splice(index, 1);

    // 如果关闭的是当前激活的标签，则需要跳转到其他标签
    if (isActive(tag)) {
      // 跳转到剩余的最后一个标签
      const latestView = visitedViews.value.slice(-1)[0];
      if (latestView) {
        router.push(latestView.path);
      } else {
        // 如果没有剩余标签，跳转到首页
        router.push('/');
      }
    }
  }
};

// 打开右键菜单
const openMenu = (tag: TagView, e: MouseEvent): void => {
  left.value = e.clientX;
  top.value = e.clientY;
  visible.value = true;
  selectedTag.value = tag;
};

// 关闭右键菜单
const closeMenu = (): void => {
  visible.value = false;
};

// 刷新选中标签
const refreshSelectedTag = (tag: TagView): void => {
  visible.value = false;

  // 实现页面刷新
  const { fullPath } = tag;
  router
    .replace({
      path: '/redirect' + fullPath,
    })
    .catch(() => {
      // 如果没有设置重定向路由，则使用强制刷新
      const { path } = tag;
      nextTick(() => {
        router.replace({ path });
      });
    });
};

// 关闭选中标签
const closeSelectedTag = (tag: TagView): void => {
  closeTagView(tag);
  visible.value = false;
};

// 关闭其他标签
const closeOthersTags = (): void => {
  if (!selectedTag.value || !selectedTag.value.path) return;

  // 保留首页和当前选中的标签
  visitedViews.value = visitedViews.value.filter(
    tag => tag.path === selectedTag.value.path || tag.path === '/home'
  );
  visible.value = false;

  // 如果当前路由不在保留的标签中，则跳转到首页
  const isCurrentRoutePreserved = visitedViews.value.some(tag => tag.path === route.path);
  if (!isCurrentRoutePreserved) {
    router.push('/home');
  }
};

// 关闭所有标签
const closeAllTags = (): void => {
  // 保留首页
  visitedViews.value = visitedViews.value.filter(tag => tag.path === '/home');
  visible.value = false;

  // 如果当前路由不是首页，跳转到首页
  if (route.path !== '/home') {
    router.push('/home');
  }
};

// 监听路由变化，添加访问视图
watch(
  () => route.path,
  () => {
    // 添加当前路由到访问视图
    addVisitedView(route);
    // 关闭右键菜单
    closeMenu();
  },
  { immediate: true }
);

// 添加点击外部关闭菜单的事件
onMounted(() => {
  // 确保首页标签始终存在
  if (!visitedViews.value.some(tag => tag.path === '/home')) {
    visitedViews.value.push(defaultTag);
  }

  // 添加当前路由
  addVisitedView(route);

  // 添加点击事件监听器来关闭右键菜单
  document.addEventListener('click', closeMenu);
});
</script>

<style scoped>
.tags-view-container {
  height: 34px;
  width: 100%;
  background: #fff;
  border-bottom: 1px solid #d8dce5;
  box-shadow:
    0 1px 3px 0 rgba(0, 0, 0, 0.12),
    0 0 3px 0 rgba(0, 0, 0, 0.04);
  position: relative;
  z-index: 10;
  display: flex;
  align-items: center;
}

.tags-view-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  padding: 0 16px;
  overflow-x: auto;
  overflow-y: hidden;
  white-space: nowrap;
}

.tags-view-wrapper :deep(.el-scrollbar__wrap) {
  display: flex;
  align-items: center;
  height: 100%;
}

.tags-view-wrapper :deep(.el-scrollbar__view) {
  display: flex;
  align-items: center;
  height: 100%;
}

.tags-view-item {
  display: inline-flex;
  align-items: center;
  height: 26px;
  max-width: 150px;
  margin-right: 6px;
  cursor: pointer;
  position: relative;
  color: #495060;
  background: #f8f8f8;
  border: 1px solid #d8dce5;
  border-radius: 4px;
  padding: 0 10px;
  font-size: 12px;
  white-space: nowrap;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
}

.tags-view-item.active {
  background-color: #409eff;
  color: #fff;
  border-color: #409eff;
}

.tags-view-item span {
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.el-icon-close {
  margin-left: 5px;
  border-radius: 50%;
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  height: 16px;
  width: 16px;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tags-view-item:not(.active) .el-icon-close:hover {
  background-color: #b4bccc;
  color: #fff;
}

.tags-view-item.active .el-icon-close {
  color: #fff;
}

.tags-view-item.active .el-icon-close:hover {
  background-color: rgba(255, 255, 255, 0.3);
}

.contextmenu {
  position: fixed;
  margin: 0;
  padding: 5px 0;
  background: #fff;
  z-index: 3000;
  border-radius: 4px;
  list-style-type: none;
  box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, 0.2);
}

.contextmenu li {
  margin: 0;
  padding: 7px 16px;
  cursor: pointer;
  font-size: 12px;
}

.contextmenu li:hover {
  background: #eee;
}
</style>
