import request from '@/utils/request';

// 邀请记录类型定义
export interface InviteRecord {
  id: number;
  nick_name: string;
  tell: string;
  share_id: string;
  created_at: string;
  updated_at: string;
}

// 邀请列表响应类型
export interface InviteListResponse {
  list: InviteRecord[];
  total: number;
}

/**
 * 获取用户邀请列表
 * @param params 查询参数
 * @returns 邀请列表数据
 */
export function getUserInviteList(params: {
  page: number;
  page_size: number;
  tell?: string;
  nick_name?: string;
}) {
  return request<InviteListResponse>({
    url: '/api/v1/user/share',
    method: 'get',
    params,
  });
}
