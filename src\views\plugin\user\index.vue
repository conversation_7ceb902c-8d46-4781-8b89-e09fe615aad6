<template>
  <div class="user-management-container">
    <!-- 搜索区域 -->
    <el-card class="search-card">
      <el-form
        :inline="true"
        :model="searchForm"
        class="search-form"
      >
        <el-form-item label="手机号">
          <el-input
            v-model="searchForm.tell"
            placeholder="请输入手机号"
            clearable
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            @click="handleSearch"
          >
            查询
          </el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 表格操作区域 -->
    <el-card class="table-card">
      <div class="table-operations">
        <el-button
          type="primary"
          @click="handleAdd"
        >
          新增用户
        </el-button>
        <el-button
          type="danger"
          :disabled="selectedRows.length === 0"
          @click="handleBatchDelete"
        >
          批量删除
        </el-button>
        <el-button @click="handleRefresh">刷新</el-button>
      </div>

      <!-- 用户数据表格 -->
      <el-table
        v-loading="tableLoading"
        :data="tableData"
        border
        stripe
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          type="selection"
          width="55"
          align="center"
        />
        <el-table-column
          prop="id"
          label="用户ID"
          width="80"
          align="center"
        />
        <el-table-column
          prop="tell"
          label="手机号"
          width="180"
        />
        <el-table-column
          prop="nick_name"
          label="昵称"
        />
        <!-- 是否到期 -->
        <el-table-column
          label="插件状态"
          width="100"
        >
          <template #default="scope">
            <el-tag
              :type="isDateBeforeCurrent(scope.row.user_package.EndTime) ? 'danger' : 'success'"
            >
              {{ isDateBeforeCurrent(scope.row.user_package.EndTime) ? '已到期' : '未到期' }}
            </el-tag>
          </template>
        </el-table-column>
        <!-- 邀请人 -->
        <el-table-column
          prop="share_id"
          label="邀请人ID"
          width="100"
        >
          <template #default="scope">
            {{ scope.row.share_id ? scope.row.share_id : '无' }}
          </template>
        </el-table-column>
        <el-table-column
          prop="user_package.EndTime"
          label="到期时间"
          width="180"
        >
          <template #default="scope">
            {{ formatDate(scope.row.user_package.EndTime) }}
          </template>
        </el-table-column>
        <el-table-column
          label="更新时间"
          width="180"
        >
          <template #default="scope">
            {{ formatDate(scope.row.updated_at) }}
          </template>
        </el-table-column>
        <el-table-column
          label="创建时间"
          width="180"
        >
          <template #default="scope">
            {{ formatDate(scope.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          fixed="right"
          width="340"
        >
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              @click="handleEdit(scope.row)"
            >
              编辑
            </el-button>
            <el-button
              type="primary"
              size="small"
              @click="handleViewDetail(scope.row)"
            >
              查看
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="handleDelete(scope.row)"
            >
              删除
            </el-button>
            <el-button
              type="primary"
              size="small"
              @click="handleAddView(scope.row)"
            >
              开通套餐
            </el-button>
            <el-button
              type="warning"
              size="small"
              @click="handleLogout(scope.row)"
            >
              下线
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑用户对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'add' ? '新增用户' : '编辑用户'"
      width="600px"
      @close="resetForm"
    >
      <el-form
        ref="userFormRef"
        :model="userForm"
        :rules="userFormRules"
        label-width="80px"
        label-position="right"
      >
        <el-form-item
          label="手机号"
          prop="tell"
        >
          <el-input
            v-model="userForm.tell"
            placeholder="请输入手机号"
          />
        </el-form-item>
        <el-form-item
          label="昵称"
          prop="nick_name"
        >
          <el-input
            v-model="userForm.nick_name"
            placeholder="请输入昵称"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="submitForm"
          >
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 开通套餐 -->
    <el-drawer
      v-model="addPackageVisible"
      title="开通套餐"
      size="30%"
    >
      <div
        v-if="currentUser"
        class="user-detail"
      >
        <div class="user-detail-header">
          <h3>用户信息：</h3>
          <p>手机号：{{ currentUser.tell }}</p>
          <p>昵称：{{ currentUser.nick_name || '暂无昵称' }}</p>
        </div>
        <el-form :model="addFormData">
          <el-form-item label="选择套餐">
            <div style="width: 180px">
              <el-select
                v-model="addFormData.package_id"
                placeholder="请选择套餐"
                clearable
              >
                <el-option
                  v-for="item in packageList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                  :disabled="item.disabled"
                />
              </el-select>
            </div>
          </el-form-item>
          <el-form-item>
            <div
              class="user-detail-header"
              style="width: 100%"
            >
              <el-button
                type="primary"
                @click="addUserPackage"
              >
                确定
              </el-button>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </el-drawer>

    <!-- 用户详情抽屉 -->
    <el-drawer
      v-model="drawerVisible"
      title="用户详情"
      size="50%"
    >
      <div
        v-if="currentUser"
        class="user-detail"
      >
        <div class="user-detail-header">
          <h3>{{ currentUser.tell }}</h3>
          <p>{{ currentUser.nick_name || '暂无昵称' }}</p>
        </div>
        <el-tabs>
          <el-tab-pane label="基本信息">
            <el-descriptions
              title="基本信息"
              :column="1"
              border
            >
              <el-descriptions-item label="用户ID">
                {{ currentUser.id }}
              </el-descriptions-item>
              <el-descriptions-item label="手机号">
                {{ currentUser.tell }}
              </el-descriptions-item>
              <el-descriptions-item label="昵称">
                {{ currentUser.nick_name || '暂无昵称' }}
              </el-descriptions-item>
              <el-descriptions-item label="创建时间">
                {{ formatDate(currentUser.created_at) }}
              </el-descriptions-item>
              <el-descriptions-item label="更新时间">
                {{ formatDate(currentUser.updated_at) }}
              </el-descriptions-item>
            </el-descriptions>
          </el-tab-pane>
          <el-tab-pane label="登录记录">
            <div
              v-infinite-scroll="() => getLoginRecords(true)"
              :infinite-scroll-disabled="loginRecordsLoading || loginRecordsFinished"
              :infinite-scroll-distance="10"
              class="login-records-container"
            >
              <el-table
                v-loading="loginRecordsLoading"
                :data="loginRecords"
                border
                stripe
                style="width: 100%"
              >
                <el-table-column
                  prop="ip"
                  label="登录IP"
                  width="120"
                />
                <el-table-column
                  prop="machine_code"
                  label="设备号"
                  min-width="120"
                />
                <el-table-column
                  prop="created_at"
                  label="登录时间"
                  width="160"
                >
                  <template #default="scope">
                    {{ formatDate(scope.row.created_at) }}
                  </template>
                </el-table-column>
              </el-table>
              <div
                v-if="loginRecordsFinished && loginRecords.length > 0"
                class="login-records-finished"
              >
                没有更多数据了
              </div>
              <el-empty
                v-if="!loginRecordsLoading && loginRecords.length === 0"
                description="暂无登录记录"
              />
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import type { FormInstance, FormRules } from 'element-plus';
import {
  getUserList,
  addUser,
  updateUser,
  deleteUser,
  batchDeleteUsers,
  openPackage,
  User,
  getUserLoginRecords,
  LoginRecord,
  logoutUser,
} from '@/api/plugin/user';
import { getPackageList } from '@/api/plugin/package';

import { formatDate, isDateBeforeCurrent } from '@/utils/tools';

// 添加套餐
const addPackageVisible = ref<boolean>(false);
const addFormData = reactive({
  package_id: null,
});
interface packageType {
  label: string | number;
  value: string;
  disabled: boolean;
}
// 套餐列表
let packageList = reactive<packageType[]>([]);

// 处理查看用户详情
const handleAddView = (row: User) => {
  currentUser.value = { ...row };
  getPackageData();
};
// 获取套餐列表
const getPackageData = async () => {
  try {
    const res = await getPackageList({
      page: 1,
      page_size: 30,
    });
    console.log(res);
    packageList = res.list.map(item => {
      return {
        disabled: item.is_active !== 1,
        value: item.id,
        label: item.title,
      };
    }) as unknown as packageType[];
    addPackageVisible.value = true;
  } catch (error) {
    console.error('获取套餐列表失败', error);
    ElMessage.error('获取套餐列表失败');
  }
};
// 给用户添加套餐
const addUserPackage = async () => {
  if (!addFormData.package_id) {
    ElMessage.error('请选择要开通的套餐');
    return;
  }
  if (!currentUser.value?.id) {
    ElMessage.error('请选择用户');
    return;
  }
  try {
    await openPackage({
      uid: currentUser.value?.id,
      package_id: addFormData.package_id,
    });
    ElMessage.success('开通套餐成功');
    addPackageVisible.value = false;
    getUserListData();
  } catch (error) {
    console.error('开通套餐失败', error);
    ElMessage.error('开通套餐失败');
  }
};

// 搜索表单
const searchForm = reactive({
  tell: '',
});

// 表格数据
const tableData = ref<User[]>([]);
const tableLoading = ref(false);

// 选中的行
const selectedRows = ref<User[]>([]);

// 分页信息
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0,
});

// 对话框相关
const dialogVisible = ref(false);
const dialogType = ref<'add' | 'edit'>('add');
const userFormRef = ref<FormInstance>();
const userForm = reactive<Partial<User> & { confirmPassword?: string }>({
  nick_name: '',
  tell: '',
});

// 表单验证规则
const userFormRules = reactive<FormRules>({
  tell: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' },
  ],
  nick_name: [{ max: 20, message: '长度不能超过20个字符', trigger: 'blur' }],
});

// 用户详情抽屉
const drawerVisible = ref(false);
const currentUser = ref<User>();

// 登录记录相关
const loginRecords = ref<LoginRecord[]>([]);
const loginRecordsLoading = ref(false);
const loginRecordsFinished = ref(false);
const loginRecordsPage = ref(1);
const loginRecordsPageSize = ref(10);
const loginRecordsTotal = ref(0);

// 获取登录记录
const getLoginRecords = async (isLoadMore = false) => {
  if (!currentUser.value?.id || loginRecordsLoading.value || loginRecordsFinished.value) return;
  loginRecordsLoading.value = true;
  try {
    const res = await getUserLoginRecords(currentUser.value.id, {
      page: loginRecordsPage.value,
      page_size: loginRecordsPageSize.value,
    });
    if (isLoadMore) {
      loginRecords.value = [...loginRecords.value, ...res.list];
    } else {
      loginRecords.value = res.list;
    }
    loginRecordsTotal.value = res.total;
    loginRecordsFinished.value = loginRecords.value.length >= res.total;
    if (!loginRecordsFinished.value) {
      loginRecordsPage.value++;
    }
  } catch (error) {
    console.error('获取登录记录失败', error);
    ElMessage.error('获取登录记录失败');
  } finally {
    loginRecordsLoading.value = false;
  }
};

// 重置登录记录状态
const resetLoginRecords = () => {
  loginRecords.value = [];
  loginRecordsLoading.value = false;
  loginRecordsFinished.value = false;
  loginRecordsPage.value = 1;
  loginRecordsTotal.value = 0;
};

// 获取用户列表数据
const getUserListData = async () => {
  tableLoading.value = true;
  try {
    // 构建查询参数
    const params = {
      page: pagination.currentPage,
      page_size: pagination.pageSize,
      tell: searchForm.tell || undefined,
    };

    try {
      // 获取数据，返回结构已经在axios拦截器中处理
      const response = (await getUserList(params)) as unknown as { list: User[]; total: number };
      if (response && response.list && typeof response.total === 'number') {
        tableData.value = response.list;
        pagination.total = response.total;
      } else {
        ElMessage.warning('获取用户列表数据结构不符合预期');
        console.error('返回数据结构不符合预期', response);
      }
    } catch (error) {
      console.error('获取用户列表失败', error);
      ElMessage.error('获取用户列表失败');
    }
  } finally {
    tableLoading.value = false;
  }
};

// 重置搜索表单
const resetSearch = () => {
  searchForm.tell = '';
  handleSearch();
};

// 处理搜索
const handleSearch = () => {
  pagination.currentPage = 1;
  getUserListData();
};

// 处理刷新
const handleRefresh = () => {
  getUserListData();
};

// 处理表格选择变化
const handleSelectionChange = (rows: User[]) => {
  selectedRows.value = rows;
};

// 处理分页大小变化
const handleSizeChange = (size: number) => {
  pagination.pageSize = size;
  getUserListData();
};

// 处理当前页变化
const handleCurrentChange = (page: number) => {
  pagination.currentPage = page;
  getUserListData();
};

// 处理新增用户
const handleAdd = () => {
  dialogType.value = 'add';
  dialogVisible.value = true;
  resetForm();
};

// 处理编辑用户
const handleEdit = (row: User) => {
  dialogType.value = 'edit';
  dialogVisible.value = true;
  // 复制用户数据到表单
  userForm.id = row.id;
  userForm.tell = row.tell;
  userForm.nick_name = row.nick_name;
};

// 处理查看用户详情
const handleViewDetail = (row: User) => {
  currentUser.value = { ...row };
  drawerVisible.value = true;
  resetLoginRecords();
  getLoginRecords();
};

// 处理删除用户
const handleDelete = (row: User) => {
  ElMessageBox.confirm(`确定要删除用户 "${row.tell}" 吗？`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      try {
        if (row.id) {
          await deleteUser(row.id);
          ElMessage.success('删除成功');
          getUserListData(); // 刷新列表
        } else {
          ElMessage.error('用户ID不存在，无法删除');
        }
      } catch (error) {
        console.error('删除用户失败', error);
        ElMessage.error('删除用户失败');
      }
    })
    .catch(() => {
      // 取消删除
    });
};

// 处理批量删除
const handleBatchDelete = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要删除的用户');
    return;
  }

  const userTells = selectedRows.value.map(item => item.tell).join('、');
  ElMessageBox.confirm(`确定要删除以下用户吗？<br/>${userTells}`, '警告', {
    dangerouslyUseHTMLString: true,
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      try {
        const ids = selectedRows.value
          .map(item => item.id)
          .filter((id): id is number => id !== undefined);
        await batchDeleteUsers(ids);
        ElMessage.success('批量删除成功');
        selectedRows.value = [];
        getUserListData(); // 刷新列表
      } catch (error) {
        console.error('批量删除用户失败', error);
        ElMessage.error('批量删除失败');
      }
    })
    .catch(() => {
      // 取消删除
    });
};

// 处理下线用户
const handleLogout = async (row: User) => {
  try {
    if (!row.id) {
      ElMessage.error('用户ID不存在');
      return;
    }
    await logoutUser(row.id);
    ElMessage.success('用户已下线');
  } catch (error) {
    console.error('下线用户失败', error);
    ElMessage.error('下线用户失败');
  }
};

// 重置表单
const resetForm = () => {
  userFormRef.value?.resetFields();
  Object.assign(userForm, {
    id: undefined,
    tell: '',
    nick_name: '',
  });
};

// 提交表单
const submitForm = async () => {
  if (!userFormRef.value) return;

  await userFormRef.value.validate(async (valid, fields) => {
    if (valid) {
      try {
        // 提交前处理数据
        const submitData = { ...userForm };

        console.log(submitData);

        if (dialogType.value === 'add') {
          // 添加用户
          await addUser(submitData);
          ElMessage.success('添加用户成功');
        } else {
          // 编辑用户
          if (!submitData.id) {
            throw new Error('用户ID不存在');
          }
          await updateUser(submitData);
          ElMessage.success('更新用户成功');
        }

        dialogVisible.value = false;
        getUserListData(); // 刷新列表
      } catch (error) {
        console.error('提交表单失败', error);
        ElMessage.error(dialogType.value === 'add' ? '添加用户失败' : '更新用户失败');
      }
    } else {
      console.log('验证失败', fields);
    }
  });
};

// 初始化
onMounted(() => {
  getUserListData();
});
</script>

<style scoped>
.user-management-container {
  padding: 20px;
}

.search-card {
  margin-bottom: 20px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.table-card {
  margin-bottom: 20px;
}

.table-operations {
  margin-bottom: 16px;
  display: flex;
  justify-content: flex-start;
  gap: 10px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.avatar-uploader {
  display: flex;
  justify-content: center;
  align-items: center;
}

.avatar-uploader .el-upload {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.avatar {
  width: 100px;
  height: 100px;
  object-fit: cover;
  display: block;
}

.user-detail {
  padding: 0 20px;
}

.user-detail-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30px;
}

.user-detail-header h3 {
  margin: 10px 0 5px;
  font-size: 18px;
}

.user-detail-header p {
  margin: 0;
  color: #909399;
}

.login-records-container {
  height: calc(100vh - 300px);
  overflow-y: auto;
}

.login-records-finished {
  text-align: center;
  color: #909399;
  font-size: 14px;
  padding: 10px 0;
}

:deep(.el-tabs__content) {
  padding: 20px 0;
}
</style>
