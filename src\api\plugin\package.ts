import request from '@/utils/request';

// 套餐管理接口
export interface Package {
  id: number;
  title: string;
  description: string;
  validity_days: string;
  price: string;
  is_active: number;
  start_date: string;
  end_date: string;
}

// 套餐列表响应接口
export interface PackageListResponse {
  list: Package[];
  total: number;
}

// 新增套餐
export function addPackage(data: Package) {
  return request<Package>({
    url: '/api/v1/member/package/',
    method: 'post',
    data,
  });
}

// 获取套餐列表
export function getPackageList(params: any) {
  return request<PackageListResponse>({
    url: '/api/v1/member/package/',
    method: 'get',
    params,
  });
}

// 更新套餐
export function updatePackage(data: Package) {
  return request<Package>({
    url: `/api/v1/member/package/${data.id}`,
    method: 'put',
    data,
  });
}

// 删除套餐
export function deletePackage(id: number) {
  return request<null>({
    url: `/api/v1/member/package/${id}`,
    method: 'delete',
  });
}
