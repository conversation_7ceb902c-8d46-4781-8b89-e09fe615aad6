import axios, { AxiosRequestConfig } from 'axios';
import { ElMessage } from 'element-plus';
import { getToken, removeToken, removeUserInfo } from '@/utils/auth';
import router from '@/router';

// 创建axios实例
const service = axios.create({
  baseURL: import.meta.env.VITE_APP_BASE_API || '',
  timeout: 10000, // 请求超时时间
  headers: {
    'Content-Type': 'application/json;charset=utf-8',
  },
});

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 从localStorage中获取token并添加到请求头中
    const token = getToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  error => {
    console.log(error);
    return Promise.reject(error);
  }
);

// 响应拦截器
service.interceptors.response.use(
  response => {
    const res = response.data;
    console.log('API响应数据:', res); // 添加调试日志

    // 根据自定义状态码判断请求是否成功
    if (res.code !== 0) {
      ElMessage({
        message: res.msg || '系统错误',
        type: 'error',
        duration: 3 * 1000,
      });

      // 401: Token过期或未登录
      if (res.code === 401) {
        console.log('检测到401状态码，准备跳转到登录页...');
        removeToken();
        removeUserInfo();
        router.replace('/login');
        return Promise.reject(new Error(res.msg || '未授权，请重新登录'));
      }

      return Promise.reject(new Error(res.msg || '系统错误'));
    } else {
      return res.data;
    }
  },
  error => {
    console.log('响应错误：', error);

    // 记录网络错误日志
    const errorList = localStorage.getItem('networkErrors')
      ? JSON.parse(localStorage.getItem('networkErrors') || '[]')
      : [];

    let message = '连接服务器失败';

    if (error.response) {
      switch (error.response.status) {
        case 401:
          message = '未授权，请重新登录';
          removeToken();
          removeUserInfo();
          console.log('检测到401 HTTP状态，准备跳转到登录页...');
          router.replace('/login');
          break;
        case 403:
          message = '拒绝访问';
          break;
        case 404:
          message = '请求错误，未找到该资源';
          break;
        case 500:
          message = '服务器内部错误';
          break;
        default:
          message = error.response.data?.msg || `连接错误${error.response.status}`;
      }

      // 添加错误到列表
      errorList.push({
        url: error.config.url,
        status: error.response.status,
        message: message,
        time: new Date().toISOString(),
      });
    } else if (error.request) {
      // 请求已发送但没有收到响应
      message = '服务器无响应，请检查网络连接';

      errorList.push({
        url: error.config?.url,
        message: '网络请求超时或无响应',
        time: new Date().toISOString(),
      });
    } else {
      // 请求配置有错误
      message = error.message || '请求配置错误';

      errorList.push({
        message: error.message,
        time: new Date().toISOString(),
      });
    }

    // 保存错误列表，最多保存10条
    localStorage.setItem('networkErrors', JSON.stringify(errorList.slice(-10)));
    console.log('网络错误清单:', errorList);

    ElMessage({
      message: message,
      type: 'error',
      duration: 5 * 1000,
    });
    return Promise.reject(error);
  }
);

// 使用泛型增强request方法的类型
const request = <T>(config: AxiosRequestConfig): Promise<T> => {
  return service(config) as Promise<T>;
};

/**
 * 文件下载方法
 * @param {AxiosRequestConfig} config - axios请求配置
 * @param {string} [defaultFileName] - 默认文件名（如果响应头中没有提供）
 * @returns {Promise<void>} 下载成功返回void，失败抛出错误
 */
export const downloadFile = (
  config: AxiosRequestConfig,
  defaultFileName: string = 'download.csv'
): Promise<void> => {
  // 设置响应类型为blob
  const downloadConfig: AxiosRequestConfig = {
    ...config,
    baseURL: import.meta.env.VITE_APP_BASE_API || '',
    responseType: 'blob',
    timeout: 60000, // 下载文件可能需要更长的超时时间
  };

  // 添加token到请求头
  const token = getToken();
  if (token) {
    downloadConfig.headers = {
      ...downloadConfig.headers,
      Authorization: `Bearer ${token}`,
    };
  }

  return new Promise((resolve, reject) => {
    axios(downloadConfig)
      .then(response => {
        // 检查响应的内容类型
        const contentType = response.headers['content-type'] || '';

        // 如果是JSON类型，可能是错误响应
        if (contentType.includes('application/json')) {
          const reader = new FileReader();
          reader.onload = () => {
            try {
              const result = JSON.parse(reader.result as string);
              // 检查code是否为0，只有code为0才表示成功
              if (result.code !== 0) {
                const errorMessage = result.msg || '下载失败';
                ElMessage.error(errorMessage);
                return reject(new Error(errorMessage));
              }
              // code为0，处理下载逻辑
              handleDownload(response, defaultFileName, resolve);
            } catch {
              // 解析JSON失败，假设是二进制文件，继续下载
              handleDownload(response, defaultFileName, resolve);
            }
          };
          reader.onerror = () => {
            ElMessage.error('解析响应失败');
            reject(new Error('解析响应失败'));
          };
          reader.readAsText(response.data);
        } else {
          // 非JSON类型，直接下载
          handleDownload(response, defaultFileName, resolve);
        }
      })
      .catch(error => {
        let errorMessage = '下载文件失败';

        // 尝试解析错误响应
        if (error.response && error.response.data) {
          try {
            // 如果服务器返回的是JSON错误信息
            const reader = new FileReader();
            reader.onload = () => {
              try {
                const errorData = JSON.parse(reader.result as string);
                errorMessage = errorData.msg || errorData.message || errorMessage;
              } catch (e) {
                console.error('解析错误响应失败', e);
              }
              ElMessage.error(errorMessage);
              reject(new Error(errorMessage));
            };
            reader.onerror = () => {
              ElMessage.error(errorMessage);
              reject(new Error(errorMessage));
            };
            reader.readAsText(error.response.data);
          } catch {
            ElMessage.error(errorMessage);
            reject(error);
          }
        } else {
          ElMessage.error(errorMessage);
          reject(error);
        }
      });
  });
};

// 辅助函数：处理文件下载
const handleDownload = (response: any, defaultFileName: string, resolve: () => void) => {
  // 从响应头中获取文件名
  const contentDisposition = response.headers['content-disposition'];
  let fileName = defaultFileName;

  if (contentDisposition) {
    const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
    const matches = filenameRegex.exec(contentDisposition);
    if (matches != null && matches[1]) {
      fileName = matches[1].replace(/['"]/g, '');
      // 解码文件名（处理中文等非ASCII字符）
      try {
        fileName = decodeURIComponent(fileName);
      } catch (e) {
        console.warn('解码文件名失败，使用原始文件名', e);
      }
    }
  }

  // 创建Blob对象
  const blob = new Blob([response.data], {
    type: response.headers['content-type'] || 'application/octet-stream',
  });

  // 创建下载链接
  const downloadLink = document.createElement('a');
  const url = URL.createObjectURL(blob);
  downloadLink.href = url;
  downloadLink.download = fileName;
  document.body.appendChild(downloadLink);
  downloadLink.click();

  // 清理
  document.body.removeChild(downloadLink);
  URL.revokeObjectURL(url);
  resolve();
};

export default request;
