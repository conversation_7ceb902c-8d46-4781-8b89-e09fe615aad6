#!/usr/bin/env node

/**
 * 简单的ESLint检查和修复脚本
 * 使用方法：node scripts/lint-fix.js
 */

import { execSync } from 'child_process';
import { join } from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const rootPath = join(__dirname, '..');

try {
  console.log('开始运行ESLint检查...');
  execSync('eslint --ext .js,.ts,.vue src', { 
    cwd: rootPath, 
    stdio: 'inherit' 
  });
  console.log('ESLint检查完成，未发现问题！');
} catch (error) {
  console.log('\n发现ESLint问题，正在尝试自动修复...');
  try {
    execSync('eslint --ext .js,.ts,.vue src --fix', { 
      cwd: rootPath, 
      stdio: 'inherit' 
    });
    console.log('ESLint自动修复完成！');
  } catch (fixError) {
    console.error('ESLint自动修复失败，某些问题可能需要手动解决。');
    process.exit(1);
  }
} 