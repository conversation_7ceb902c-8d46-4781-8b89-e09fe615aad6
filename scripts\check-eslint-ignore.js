#!/usr/bin/env node

/**
 * 此脚本用于检查ESLint是否忽略特定文件
 */

import { ESLint } from "eslint";
import { fileURLToPath } from "url";
import { dirname, resolve } from "path";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const rootDir = resolve(__dirname, "..");

async function checkIfFileIsIgnored() {
  // 创建新的ESLint实例
  const eslint = new ESLint({
    cwd: rootDir,
    overrideConfigFile: resolve(rootDir, "eslint.config.js")
  });

  // 检查文件是否被忽略
  const filesToCheck = [
    resolve(rootDir, "src/mock/index.ts"),
    resolve(rootDir, "src/mock/user.ts"),
    resolve(rootDir, "src/mock/types.d.ts")
  ];

  console.log("检查ESLint是否忽略以下文件:");
  
  for (const file of filesToCheck) {
    const isIgnored = await eslint.isPathIgnored(file);
    console.log(`- ${file}: ${isIgnored ? "已忽略 ✓" : "未忽略 ✗"}`);
  }
}

checkIfFileIsIgnored().catch(error => {
  console.error("检查出错:", error);
  process.exit(1);
}); 