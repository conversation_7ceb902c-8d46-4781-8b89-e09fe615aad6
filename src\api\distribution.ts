/**
 * 分销明细API模块
 * 提供分销相关的查询接口
 * @module api/distribution
 */
import request from '@/utils/request';

/**
 * 分销明细数据接口
 * @interface DistributionDetail
 */
export interface DistributionDetail {
  /** 记录ID */
  id: number;
  /** 分销订单号 */
  OutTradeNo: string;
  /** 用户账号 */
  Uid: string;
  /** 订单金额 */
  Total: number;
  /** 结算状态：-1-未结算，1-已结算 */
  SettlementStatus: number;
  /** 创建时间 */
  CreatedAt: string;
  /** 结算时间 */
  SettlementTime?: string;
}

export interface DistributionList {
  list: DistributionDetail[];
  brokerage: number;
  order_total: number;
  settlement_num: number;
}

/**
 * 获取分销明细列表
 * @param {Object} params - 查询参数
 * @param {number} [params.user_id] - 用户ID，不传则查询所有
 * @param {number} [params.page] - 页码
 * @param {number} [params.page_size] - 每页条数
 * @param {string} [params.start_time] - 开始时间
 * @param {string} [params.end_time] - 结束时间
 * @returns {Promise<{ list: DistributionList; total: number }>} 分销明细列表和总数
 */
export function getDistributionList(params: any) {
  return request<{ list: DistributionList; total: number }>({
    url: '/api/v1/order/user/share',
    method: 'get',
    params,
  });
}

/**
 * 结算分销订单
 * @param {Object} data - 结算参数
 * @param {number} data.share_id - 分销用户ID
 * @param {string} data.start_time - 开始时间
 * @param {string} data.end_time - 结束时间
 * @param {string} [data.out_trade_no] - 订单号（非必填）
 * @returns {Promise<any>} 结算结果
 */
export function settlementDistribution(data: {
  share_id: number | string;
  start_time: string;
  end_time: string;
  out_trade_no?: string;
}) {
  return request<any>({
    url: '/api/v1/order/settlement',
    method: 'post',
    data,
  });
}
