import js from "@eslint/js";
import globals from "globals";
import tseslint from "typescript-eslint";
import pluginVue from "eslint-plugin-vue";
import pluginPrettier from "eslint-plugin-prettier";

// 明确设置忽略的文件和目录
const ignorePatterns = [
  "**/node_modules/**",
  "**/dist/**",
  "**/public/**",
  "**/*.svg",
  "**/*.scss",
  "**/*.css",
  "**/*.png",
  "**/*.jpg",
  "**/*.jpeg",
  "**/*.gif",
  "**/*.json",
  "**/*.md",
  "**/.vscode/**",
  "**/src/mock/**",  // 忽略src/mock目录下的所有文件
  "**/src/mack/**"   // 忽略src/mack目录下的所有文件
];

// 在ESLint 9.x中，直接导出一个配置数组，而不是使用configs属性
export default [
  // 设置忽略的文件
  { ignores: ignorePatterns },
  
  // 全局基础配置 - 在扁平配置中直接使用js.configs.recommended而不是extends
  { 
    files: ["**/*.{js,mjs,cjs,ts,vue}"], 
    plugins: { js }, 
    ...js.configs.recommended
  },
  
  // 全局浏览器环境配置
  { 
    files: ["**/*.{js,mjs,cjs,ts,vue}"], 
    languageOptions: { 
      globals: { 
        ...globals.browser,
        ...globals.es2021
      },
      parserOptions: {
        ecmaVersion: 'latest',
        sourceType: 'module'
      }
    } 
  },
  
  // TypeScript 配置
  ...tseslint.configs.recommended,
  
  // Vue 配置
  ...pluginVue.configs["flat/recommended"],
  
  // Vue 文件中使用 TypeScript
  { 
    files: ["**/*.vue"], 
    languageOptions: { 
      parserOptions: { 
        parser: tseslint.parser,
        extraFileExtensions: ['.vue'] 
      } 
    } 
  },
  
  // 添加 Prettier 配置
  {
    files: ["**/*.{js,mjs,cjs,ts,vue}"],
    plugins: {
      prettier: pluginPrettier
    },
    rules: {
      "prettier/prettier": ["error", {
        // 这里可以内联Prettier配置，但我们优先使用.prettierrc.js的配置
        // 如果有配置冲突，可以在这里覆盖
      }],
      // 避免与Prettier冲突的规则
      "arrow-body-style": "off", 
      "prefer-arrow-callback": "off"
    }
  },
  
  // 自定义规则
  {
    files: ["**/*.{js,mjs,cjs,ts,vue}"],
    rules: {
      // 允许未使用的变量以_开头
      'no-unused-vars': ['error', { 'argsIgnorePattern': '^_', 'varsIgnorePattern': '^_' }],
      // 关闭一些在开发中可能过于严格的规则
      'vue/multi-word-component-names': 'off',
      'vue/require-default-prop': 'off',
      // 关闭单行HTML元素内容换行规则
      'vue/singleline-html-element-content-newline': 'off',
      // 允许使用 any 类型
      '@typescript-eslint/no-explicit-any': 'off',
      '@typescript-eslint/no-any': 'off',
      // 配置HTML元素自闭合规则
      'vue/html-self-closing': ['error', {
        'html': {
          'void': 'any',
          'normal': 'always',
          'component': 'always'
        },
        'svg': 'always',
        'math': 'always'
      }],
      // Vue 模板中的属性规则，每行一个属性
      'vue/max-attributes-per-line': ['error', {
        'singleline': {
          'max': 1
        },      
        'multiline': {
          'max': 1
        }
      }]
    }
  }
];