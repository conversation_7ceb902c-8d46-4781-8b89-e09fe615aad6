import Mock from 'mockjs';
import { StatsData, ChartData, TodoItem, NoticeItem, UserRankingItem } from '@/api/types';

const Random = Mock.Random;

// 生成模拟统计数据
const generateStatsData = (): StatsData => {
  return {
    userCount: Random.integer(1000, 10000),
    orderCount: Random.integer(500, 5000),
    favoriteCount: Random.integer(2000, 20000),
    articleCount: Random.integer(100, 1000),
    userIncrease: Random.integer(-10, 50),
    orderIncrease: Random.integer(-10, 50),
    favoriteIncrease: Random.integer(-10, 50),
    articleIncrease: Random.integer(-10, 50),
  };
};

// 生成模拟图表数据
const generateChartData = (type: string): ChartData => {
  const days = type === 'week' ? 7 : type === 'month' ? 30 : 12;
  const categories = [];
  const visitorData = [];
  const orderData = [];

  for (let i = 0; i < days; i++) {
    if (type === 'week') {
      categories.push(['周日', '周一', '周二', '周三', '周四', '周五', '周六'][i]);
    } else if (type === 'month') {
      categories.push(`${i + 1}日`);
    } else {
      categories.push(`${i + 1}月`);
    }
    visitorData.push(Random.integer(100, 1000));
    orderData.push(Random.integer(10, 100));
  }

  return {
    categories,
    series: [
      {
        name: '访问量',
        data: visitorData,
      },
      {
        name: '订单量',
        data: orderData,
      },
    ],
  };
};

// 生成模拟待办事项
const generateTodos = (): TodoItem[] => {
  const todos = [];
  const count = Random.integer(0, 5);

  for (let i = 0; i < count; i++) {
    todos.push({
      id: Random.guid(),
      title: Random.ctitle(5, 20),
      completed: Random.boolean(),
      createTime: Random.datetime(),
    });
  }

  return todos;
};

// 生成模拟系统通知
const generateNotices = (): NoticeItem[] => {
  const notices = [];
  const count = Random.integer(0, 5);
  const types = ['primary', 'success', 'warning', 'danger', 'info'];

  for (let i = 0; i < count; i++) {
    notices.push({
      id: Random.guid(),
      title: Random.ctitle(5, 10),
      content: Random.cparagraph(1, 3),
      type: Random.pick(types) as 'primary' | 'success' | 'warning' | 'danger' | 'info',
      read: Random.boolean(),
      createTime: Random.datetime(),
    });
  }

  return notices;
};

// 生成模拟用户排行榜数据
const generateUserRanking = (): UserRankingItem[] => {
  const users = [];
  const count = 10;

  for (let i = 0; i < count; i++) {
    users.push({
      id: Random.integer(10000, 99999),
      username: Random.cname(),
      amount: Random.integer(10000, 1000000), // 金额，单位：分
      rank: i + 1
    });
  }

  // 按金额排序
  return users.sort((a, b) => b.amount - a.amount);
};

// 模拟获取统计数据接口
Mock.mock(/\/api\/dashboard\/stats/, 'get', () => {
  return {
    code: 0,
    msg: '获取成功',
    data: generateStatsData(),
  };
});

// 模拟获取图表数据接口
Mock.mock(/\/api\/dashboard\/chart/, 'get', (options: any) => {
  const url = new URL(`http://example.com${options.url}`);
  const type = url.searchParams.get('type') || 'week';

  return {
    code: 0,
    msg: '获取成功',
    data: generateChartData(type),
  };
});

// 模拟获取待办事项接口
Mock.mock(/\/api\/dashboard\/todos/, 'get', () => {
  return {
    code: 0,
    msg: '获取成功',
    data: generateTodos(),
  };
});

// 模拟获取系统通知接口
Mock.mock(/\/api\/dashboard\/notices/, 'get', () => {
  return {
    code: 0,
    msg: '获取成功',
    data: generateNotices(),
  };
});

// 模拟获取用户排行榜接口
Mock.mock(/\/api\/v1\/user\/ranking/, 'get', (options: any) => {
  const url = new URL(`http://example.com${options.url}`);
  const start_time = url.searchParams.get('start_time');
  const end_time = url.searchParams.get('end_time');
  
  // 这里可以根据start_time和end_time过滤数据
  // 但在模拟数据中，我们只是简单地生成随机数据
  
  return {
    code: 0,
    msg: '获取成功',
    data: generateUserRanking(),
  };
});

export function setupDashboardMock() {
  console.log('Dashboard mock data initialized');
}
