import Mock from 'mockjs';
import { RoleInfo, RoleForm } from '@/api/system/role';
import { ApiResponse, PageResult } from '@/api/types';
import { resultSuccess, resultError } from './utils';

// 模拟角色数据
const roleList: RoleInfo[] = [
  {
    id: '1',
    roleName: '超级管理员',
    roleKey: 'admin',
    status: 0,
    menuIds: ['1', '2', '3', '4', '5', '6', '7', '8', '9'],
    remark: '超级管理员，拥有所有权限',
    createTime: '2023-01-01 00:00:00',
    updateTime: '2023-01-01 00:00:00'
  },
  {
    id: '2',
    roleName: '普通用户',
    roleKey: 'user',
    status: 0,
    menuIds: ['1', '2', '5', '6', '8', '9'],
    remark: '普通用户，拥有基本权限',
    createTime: '2023-01-02 00:00:00',
    updateTime: '2023-01-02 00:00:00'
  },
  {
    id: '3',
    roleName: '访客',
    roleKey: 'visitor',
    status: 1,
    menuIds: ['1', '8', '9'],
    remark: '访客，仅有查看权限',
    createTime: '2023-01-03 00:00:00',
    updateTime: '2023-01-03 00:00:00'
  }
];

// 获取最大ID
let maxId = Math.max(...roleList.map(item => parseInt(item.id)));

// 根据条件筛选角色
function filterRoles(query: any): RoleInfo[] {
  let result = [...roleList];
  
  if (query.roleName) {
    result = result.filter(role => role.roleName.includes(query.roleName));
  }
  
  if (query.roleKey) {
    result = result.filter(role => role.roleKey.includes(query.roleKey));
  }
  
  if (query.status !== undefined && query.status !== null && query.status !== '') {
    result = result.filter(role => role.status === parseInt(query.status));
  }
  
  return result;
}

// 获取角色列表（分页）
Mock.mock(/\/role\/list/, 'get', (options: any) => {
  const url = new URL(options.url, 'http://localhost');
  const params = Object.fromEntries(url.searchParams.entries());
  
  const pageNum = parseInt(params.pageNum) || 1;
  const pageSize = parseInt(params.pageSize) || 10;
  
  // 过滤数据
  const filteredRoles = filterRoles(params);
  
  // 分页处理
  const startIndex = (pageNum - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const pageData = filteredRoles.slice(startIndex, endIndex);
  
  const result: PageResult<RoleInfo> = {
    records: pageData,
    total: filteredRoles.length,
    pages: Math.ceil(filteredRoles.length / pageSize),
    size: pageSize,
    current: pageNum
  };
  
  return resultSuccess(result);
});

// 获取所有角色
Mock.mock(/\/role\/all/, 'get', () => {
  return resultSuccess(roleList);
});

// 获取角色详情
Mock.mock(/\/role\/\d+/, 'get', (options: any) => {
  const url = options.url;
  const id = url.substring(url.lastIndexOf('/') + 1);
  
  const role = roleList.find(item => item.id === id);
  
  if (role) {
    return resultSuccess(role);
  } else {
    return resultError('角色不存在');
  }
});

// 新增角色
Mock.mock(/\/role$/, 'post', (options: any) => {
  const body = JSON.parse(options.body) as RoleForm;
  
  if (!body.roleName || !body.roleKey) {
    return resultError('角色名称和权限字符不能为空');
  }
  
  // 检查角色是否已存在
  if (roleList.some(item => item.roleName === body.roleName)) {
    return resultError('角色名称已存在');
  }
  
  if (roleList.some(item => item.roleKey === body.roleKey)) {
    return resultError('权限字符已存在');
  }
  
  maxId++;
  const newRole: RoleInfo = {
    id: maxId.toString(),
    roleName: body.roleName,
    roleKey: body.roleKey,
    status: body.status,
    menuIds: body.menuIds || [],
    remark: body.remark || '',
    createTime: new Date().toISOString().replace('T', ' ').substr(0, 19),
    updateTime: new Date().toISOString().replace('T', ' ').substr(0, 19)
  };
  
  roleList.push(newRole);
  
  return resultSuccess(newRole);
});

// 修改角色
Mock.mock(/\/role$/, 'put', (options: any) => {
  const body = JSON.parse(options.body) as RoleForm;
  
  if (!body.id || !body.roleName || !body.roleKey) {
    return resultError('角色ID、名称和权限字符不能为空');
  }
  
  // 查找角色
  const index = roleList.findIndex(item => item.id === body.id);
  
  if (index === -1) {
    return resultError('角色不存在');
  }
  
  // 检查名称是否重复（排除自身）
  if (roleList.some(item => item.roleName === body.roleName && item.id !== body.id)) {
    return resultError('角色名称已存在');
  }
  
  // 检查权限字符是否重复（排除自身）
  if (roleList.some(item => item.roleKey === body.roleKey && item.id !== body.id)) {
    return resultError('权限字符已存在');
  }
  
  // 更新角色
  roleList[index] = {
    ...roleList[index],
    roleName: body.roleName,
    roleKey: body.roleKey,
    status: body.status,
    menuIds: body.menuIds || roleList[index].menuIds,
    remark: body.remark !== undefined ? body.remark : roleList[index].remark,
    updateTime: new Date().toISOString().replace('T', ' ').substr(0, 19)
  };
  
  return resultSuccess(roleList[index]);
});

// 删除角色
Mock.mock(/\/role\/\d+/, 'delete', (options: any) => {
  const url = options.url;
  const id = url.substring(url.lastIndexOf('/') + 1);
  
  const index = roleList.findIndex(item => item.id === id);
  
  if (index === -1) {
    return resultError('角色不存在');
  }
  
  // 系统内置角色不允许删除
  if (id === '1') {
    return resultError('超级管理员角色不能删除');
  }
  
  roleList.splice(index, 1);
  
  return resultSuccess(null, '删除成功');
});

// 修改角色状态
Mock.mock(/\/role\/changeStatus/, 'put', (options: any) => {
  const body = JSON.parse(options.body);
  
  if (!body.id) {
    return resultError('角色ID不能为空');
  }
  
  const role = roleList.find(item => item.id === body.id);
  
  if (!role) {
    return resultError('角色不存在');
  }
  
  // 系统内置角色不允许禁用
  if (role.id === '1' && body.status === 1) {
    return resultError('超级管理员角色不能禁用');
  }
  
  role.status = body.status;
  role.updateTime = new Date().toISOString().replace('T', ' ').substr(0, 19);
  
  return resultSuccess(null, '状态修改成功');
});

// 注册角色管理的模拟数据
export const setupRoleMock = () => {
  // 这里不需要额外的注册逻辑，Mock.mock 已经在上面注册了各个接口
};

export default setupRoleMock; 