<template>
  <div class="distribution-container">
    <el-row :gutter="20">
      <!-- 左侧用户列表 -->
      <el-col :span="6">
        <div class="user-list-container">
          <div class="header">
            <el-input
              v-model="userSearchKeyword"
              placeholder="搜索用户"
              prefix-icon="Search"
              clearable
              @input="handleUserSearch"
            />
          </div>
          <div
            v-loading="userLoading"
            class="user-list"
          >
            <el-scrollbar height="calc(100vh - 240px)">
              <ul
                v-infinite-scroll="loadMoreUsers"
                :infinite-scroll-disabled="userDisabled"
                :infinite-scroll-distance="20"
              >
                <li
                  v-for="user in userList"
                  :key="user.id"
                  :class="{ active: currentUserId === user.id }"
                  @click="handleUserClick(user)"
                >
                  <div class="user-item">
                    <div class="user-name">{{ user.account }}</div>
                    <div class="user-phone">{{ user.tell || '无手机号' }}</div>
                  </div>
                </li>
                <div
                  v-if="userNoMore"
                  class="no-more"
                >
                  没有更多数据了
                </div>
                <div
                  v-if="userLoading && !userNoMore"
                  class="loading"
                >
                  加载中...
                </div>
              </ul>
            </el-scrollbar>
          </div>
        </div>
      </el-col>

      <!-- 右侧分销明细 -->
      <el-col :span="18">
        <div class="distribution-detail">
          <div class="header">
            <h3 style="margin-bottom: 10px">
              分销明细{{ currentUser ? ' - ' + currentUser.account : '' }}
            </h3>
            <div class="search-form">
              <el-form inline>
                <el-form-item label="订单号">
                  <el-input
                    v-model="searchForm.OutTradeNo"
                    placeholder="请输入订单号"
                    clearable
                  />
                </el-form-item>
                <el-form-item label="日期">
                  <el-date-picker
                    v-model="dateRange"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    value-format="YYYY-MM-DD"
                  />
                </el-form-item>
                <el-form-item>
                  <el-button
                    type="primary"
                    :disabled="!currentUserId"
                    @click="loadDistributionData"
                  >
                    查询
                  </el-button>
                  <el-button @click="resetSearch">重置</el-button>
                  <el-button
                    type="success"
                    :disabled="!currentUserId"
                    @click="handleSettlement"
                  >
                    核算
                  </el-button>
                </el-form-item>
              </el-form>
            </div>
          </div>

          <!-- 统计卡片区域 -->
          <el-row
            v-if="currentUserId"
            :gutter="20"
            class="stat-cards"
          >
            <el-col :span="8">
              <el-card shadow="hover">
                <template #header>
                  <div class="card-header">
                    <span>累计订单金额</span>
                  </div>
                </template>
                <div class="stat-value">
                  ¥ {{ (stats.totalAmount / 100)?.toFixed(2) || '0.00' }}
                </div>
              </el-card>
            </el-col>
            <el-col :span="8">
              <el-card shadow="hover">
                <template #header>
                  <div class="card-header">
                    <span>累计佣金</span>
                  </div>
                </template>
                <div class="stat-value">
                  ¥ {{ (stats.totalCommission / 100)?.toFixed(2) || '0.00' }}
                </div>
              </el-card>
            </el-col>
            <el-col :span="8">
              <el-card shadow="hover">
                <template #header>
                  <div class="card-header">
                    <span>已结算订单</span>
                  </div>
                </template>
                <div class="stat-value">{{ stats.settledCount || 0 }} 笔</div>
              </el-card>
            </el-col>
          </el-row>

          <!-- 分销明细表格 -->
          <el-table
            v-loading="distributionLoading"
            :data="distributionList"
            style="width: 100%; margin-top: 20px"
            border
          >
            <el-table-column
              prop="OutTradeNo"
              label="订单号"
              width="180"
            />
            <el-table-column
              prop="Uid"
              label="购买用户ID"
            />
            <el-table-column
              prop="PackageName"
              label="套餐名称"
            />
            <el-table-column
              prop="Total"
              label="订单金额"
              width="120"
            >
              <template #default="scope">
                <span>¥ {{ (scope.row.Total / 100).toFixed(2) }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="SettlementStatus"
              label="结算状态"
              width="100"
            >
              <template #default="scope">
                <el-tag :type="scope.row.SettlementStatus === 1 ? 'success' : 'warning'">
                  {{ scope.row.SettlementStatus === 1 ? '已结算' : '未结算' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column
              prop="CreatedAt"
              label="创建时间"
              width="180"
            >
              <template #default="scope">
                {{ formatDate(scope.row.CreatedAt) }}
              </template>
            </el-table-column>
            <el-table-column
              prop="SettlementTime"
              label="结算时间"
              width="180"
            >
              <template #default="scope">
                {{ formatDate(scope.row.SettlementTime) }}
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页器 -->
          <div class="pagination-container">
            <el-pagination
              v-model:current-page="pagination.page"
              v-model:page-size="pagination.page_size"
              :page-sizes="[10, 20, 50, 100]"
              :total="pagination.total"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getUserList, User } from '@/api/system/user';
import {
  getDistributionList,
  DistributionDetail,
  settlementDistribution,
} from '@/api/distribution';
import { formatDate } from '@/utils/tools';

// 用户列表相关状态
const userList = ref<User[]>([]);
const userLoading = ref(false);
const userNoMore = ref(false);
const userPage = ref(1);
const userPageSize = ref(20);
const userSearchKeyword = ref('');
const currentUserId = ref<number | null>(null);
const currentUser = ref<User | null>(null);

// 搜索表单
const searchForm = reactive({
  OutTradeNo: '',
});

// 分销明细相关状态
const distributionList = ref<DistributionDetail[]>([]);
const distributionLoading = ref(false);
const stats = reactive({
  totalAmount: 0,
  totalCommission: 0,
  settledCount: 0,
});
const dateRange = ref<[string, string] | null>(null);
const pagination = reactive({
  page: 1,
  page_size: 10,
  total: 0,
});

// 计算属性
const userDisabled = computed(() => {
  return userLoading.value || userNoMore.value;
});

// 加载用户列表
const loadUserList = async (isReset = false) => {
  if (isReset) {
    userPage.value = 1;
    userList.value = [];
    userNoMore.value = false;
  }

  if (userNoMore.value) return;

  userLoading.value = true;
  try {
    const params = {
      page: userPage.value,
      page_size: userPageSize.value,
      account: userSearchKeyword.value,
    };
    const res = await getUserList(params);
    console.log(res);
    if (res.list.length > 0) {
      userList.value = [...userList.value, ...res.list];
      userPage.value++;
    }
    if (res.list.length < userPageSize.value) {
      userNoMore.value = true;
    }
  } catch (error) {
    console.error('加载用户列表失败', error);
    ElMessage.error('加载用户列表失败');
  } finally {
    userLoading.value = false;
  }
};

// 加载更多用户
const loadMoreUsers = () => {
  loadUserList();
};

// 用户搜索
const handleUserSearch = () => {
  loadUserList(true);
};

// 点击用户
const handleUserClick = (user: User) => {
  currentUserId.value = user.id || null;
  currentUser.value = user;
  pagination.page = 1;
  loadDistributionData();
};

// 加载分销明细数据
const loadDistributionData = async () => {
  if (!currentUserId.value) {
    distributionList.value = [];
    pagination.total = 0;
    return;
  }

  distributionLoading.value = true;
  try {
    const params = {
      share_id: currentUserId.value,
      page: pagination.page,
      page_size: pagination.page_size,
      start_time: dateRange.value ? dateRange.value[0] : undefined,
      end_time: dateRange.value ? dateRange.value[1] : undefined,
      out_trade_no: searchForm.OutTradeNo || undefined,
    };
    const res = await getDistributionList(params);
    distributionList.value = res.list.list;
    pagination.total = res.total;

    // 使用列表数据计算统计信息
    // 因为API接口没有提供统计数据，我们从返回的列表中计算
    if (res.list && res.list.list && res.list.list.length > 0) {
      stats.totalAmount = res.list.order_total;
      stats.totalCommission = res.list.brokerage;
      stats.settledCount = res.list.settlement_num;
    } else {
      stats.totalAmount = 0;
      stats.totalCommission = 0;
      stats.settledCount = 0;
    }
  } catch (error) {
    console.error('加载分销明细失败', error);
    ElMessage.error('加载分销明细失败');
  } finally {
    distributionLoading.value = false;
  }
};

// 重置搜索条件
const resetSearch = () => {
  dateRange.value = null;
  searchForm.OutTradeNo = '';
  loadDistributionData();
};

// 分页相关方法
const handleSizeChange = (size: number) => {
  pagination.page_size = size;
  loadDistributionData();
};

const handleCurrentChange = (page: number) => {
  pagination.page = page;
  loadDistributionData();
};

// 处理结算
const handleSettlement = () => {
  if (!currentUserId.value) {
    ElMessage.warning('请先选择用户');
    return;
  }

  // if (stats.totalAmount <= 0) {
  //   ElMessage.warning('累计金额为0，无法进行核算');
  //   return;
  // }

  // 二次确认
  ElMessageBox.confirm(
    `确定要对${currentUser.value?.account || '当前用户'}进行核算结算吗？`,
    '结算确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(async () => {
      try {
        const params = {
          share_id: currentUserId.value + '',
          start_time: dateRange.value ? dateRange.value[0] : '',
          end_time: dateRange.value ? dateRange.value[1] : '',
          out_trade_no: searchForm.OutTradeNo || undefined,
        };

        // 验证必填参数
        if (!params.start_time || !params.end_time) {
          ElMessage.warning('请选择日期范围');
          return;
        }

        await settlementDistribution(params);
        ElMessage.success('核算结算成功');

        // 重新加载分销明细数据
        loadDistributionData();
      } catch (error) {
        console.error('核算结算失败', error);
        ElMessage.error('核算结算失败');
      }
    })
    .catch(() => {
      // 用户取消操作
    });
};

// 页面初始化
onMounted(() => {
  loadUserList();
});
</script>

<style scoped>
.user-list-container {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  height: calc(100vh - 230px);
  display: flex;
  flex-direction: column;
}

.header {
  padding: 15px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  /* align-items: center; */
  border-bottom: 1px solid #ebeef5;
}

.header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: bold;
}

.user-list {
  flex: 1;
  overflow: hidden;
}

.user-list ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.user-item {
  padding: 15px;
  border-bottom: 1px solid #ebeef5;
  cursor: pointer;
  transition: background-color 0.3s;
}

.user-item:hover {
  background-color: #f5f7fa;
}

li.active .user-item {
  background-color: #ecf5ff;
  border-left: 4px solid #409eff;
}

.user-name {
  font-weight: bold;
  margin-bottom: 5px;
}

.user-phone {
  color: #909399;
  font-size: 13px;
}

.no-more,
.loading {
  text-align: center;
  color: #909399;
  padding: 10px 0;
  font-size: 14px;
}

.distribution-detail {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 20px;
  height: calc(100vh - 230px);
  overflow: auto;
}

.search-form {
  margin-bottom: 20px;
}

.stat-cards {
  margin: 20px 0;
}

.card-header {
  font-weight: bold;
}

.stat-value {
  font-size: 24px;
  color: #409eff;
  text-align: center;
  font-weight: bold;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
