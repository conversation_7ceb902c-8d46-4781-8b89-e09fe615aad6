<template>
  <div class="package-container">
    <el-card class="search-card">
      <div class="action-header">
        <el-button
          type="primary"
          :icon="Plus"
          @click="handleAddPackage"
        >
          新增套餐
        </el-button>
      </div>
    </el-card>

    <!-- 套餐列表 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="packageList"
        border
        stripe
      >
        <el-table-column
          type="index"
          label="序号"
          width="80"
        />
        <el-table-column
          prop="title"
          label="套餐名称"
        />
        <el-table-column
          prop="description"
          label="套餐描述"
          show-overflow-tooltip
        />
        <el-table-column
          prop="validity_days"
          label="有效期(天)"
          width="110"
        />
        <el-table-column
          prop="price"
          label="价格"
          width="100"
        >
          <template #default="{ row }">
            <span>{{ row.price }}元</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="is_active"
          label="状态"
          width="100"
        >
          <template #default="{ row }">
            <el-tag :type="row.is_active === 1 ? 'success' : 'info'">
              {{ row.is_active === 1 ? '上架' : '下架' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="start_date"
          label="开始日期"
          width="140"
        >
          <template #default="{ row }">
            {{ formatDate(row.start_date) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="end_date"
          label="结束日期"
          width="140"
        >
          <template #default="{ row }">
            {{ formatDate(row.end_date) }}
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          width="180"
          fixed="right"
        >
          <template #default="{ row }">
            <el-button
              type="primary"
              link
              @click="handleEdit(row)"
            >
              编辑
            </el-button>
            <el-button
              type="danger"
              link
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.page_size"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </el-card>

    <!-- 套餐表单对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'add' ? '新增套餐' : '编辑套餐'"
      width="600px"
      destroy-on-close
    >
      <el-form
        ref="packageFormRef"
        :model="packageForm"
        :rules="packageRules"
        label-width="100px"
        label-position="right"
      >
        <el-form-item
          label="套餐名称"
          prop="title"
        >
          <el-input
            v-model="packageForm.title"
            placeholder="请输入套餐名称"
          />
        </el-form-item>
        <el-form-item
          label="套餐描述"
          prop="description"
        >
          <el-input
            v-model="packageForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入套餐描述"
          />
        </el-form-item>
        <el-form-item
          label="有效期(天)"
          prop="validity_days"
        >
          <el-input-number
            v-model="packageForm.validity_days"
            :min="1"
            :step="1"
          />
        </el-form-item>
        <el-form-item
          label="价格"
          prop="price"
        >
          <el-input-number
            v-model="packageForm.price"
            :min="0"
            :precision="2"
            :step="1"
            style="width: 160px"
          />
        </el-form-item>
        <el-form-item
          label="状态"
          prop="is_active"
        >
          <el-switch
            v-model="packageForm.is_active"
            :active-value="1"
            :inactive-value="-1"
            active-text="上架"
            inactive-text="下架"
          />
        </el-form-item>
        <el-form-item
          label="有效日期"
          prop="start_date"
        >
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 100%"
            @change="handleDateChange"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          :loading="submitLoading"
          @click="submitForm"
        >
          {{ dialogType === 'add' ? '新增' : '保存' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
// 套餐管理组件逻辑
import {
  addPackage,
  getPackageList,
  updatePackage,
  deletePackage,
  type Package,
} from '@/api/plugin/package';
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus } from '@element-plus/icons-vue';
import type { FormInstance, FormRules } from 'element-plus';
import { formatDate } from '@/utils/tools';

// 套餐列表数据
const packageList = ref<Package[]>([]);
const loading = ref(false);

// 分页参数
const pagination = reactive({
  page: 1,
  page_size: 10,
  total: 0,
});

// 获取套餐列表
const getPackageData = async () => {
  try {
    loading.value = true;
    const res = await getPackageList({
      page: pagination.page,
      page_size: pagination.page_size,
    });
    console.log(res);
    packageList.value = res.list;
    pagination.total = res.total;
  } catch (error) {
    console.error('获取套餐列表失败', error);
    ElMessage.error('获取套餐列表失败');
  } finally {
    loading.value = false;
  }
};

// 处理页码变化
const handlePageChange = (page: number) => {
  pagination.page = page;
  getPackageData();
};

// 处理每页数量变化
const handleSizeChange = (size: number) => {
  pagination.page_size = size;
  pagination.page = 1;
  getPackageData();
};

// 初始化
onMounted(() => {
  getPackageData();
});

// 表单相关
const packageFormRef = ref<FormInstance>();
const dialogVisible = ref(false);
const dialogType = ref<'add' | 'edit'>('add');
const submitLoading = ref(false);
const dateRange = ref<[Date, Date] | null>(null);

const packageForm = reactive<Partial<Package>>({
  id: undefined,
  title: '',
  description: '',
  validity_days: '',
  price: '',
  is_active: 1,
  start_date: '',
  end_date: '',
});

// 表单校验规则
const packageRules = reactive<FormRules>({
  title: [{ required: true, message: '请输入套餐名称', trigger: 'blur' }],
  description: [{ required: true, message: '请输入套餐描述', trigger: 'blur' }],
  validity_days: [{ required: true, message: '请输入有效期天数', trigger: 'blur' }],
  price: [{ required: true, message: '请输入套餐价格', trigger: 'blur' }],
  start_date: [{ required: true, message: '请选择有效日期范围', trigger: 'change' }],
  end_date: [{ required: true, message: '请选择有效日期范围', trigger: 'change' }],
});

// 新增套餐
const handleAddPackage = () => {
  dialogType.value = 'add';
  resetForm();
  dialogVisible.value = true;
};

// 编辑套餐
const handleEdit = (row: Package) => {
  dialogType.value = 'edit';
  resetForm();

  // 设置表单数据
  Object.assign(packageForm, row);

  // 设置日期范围
  if (row.start_date && row.end_date) {
    dateRange.value = [new Date(row.start_date), new Date(row.end_date)];
  }

  dialogVisible.value = true;
};

// 重置表单
const resetForm = () => {
  packageFormRef.value?.resetFields();
  dateRange.value = null;
  Object.assign(packageForm, {
    id: undefined,
    title: '',
    description: '',
    validity_days: '',
    price: '',
    is_active: 1,
    start_date: '',
    end_date: '',
  });
};

// 提交表单
const submitForm = async () => {
  console.log(packageForm);
  if (!packageFormRef.value) return;

  // 在提交前将日期值赋给表单，确保验证时能检查到
  if (dateRange.value) {
    // 使用ISO格式的日期时间字符串
    packageForm.start_date = dateRange.value[0].toISOString();
    packageForm.end_date = dateRange.value[1].toISOString();
  }

  await packageFormRef.value.validate(async valid => {
    if (!valid) return;

    try {
      submitLoading.value = true;

      if (dialogType.value === 'add') {
        await addPackage(packageForm as Package);
        ElMessage.success('新增套餐成功');
      } else {
        await updatePackage(packageForm as Package);
        ElMessage.success('更新套餐成功');
      }

      dialogVisible.value = false;
      getPackageData(); // 刷新列表
    } catch (error) {
      console.error('保存套餐失败', error);
      ElMessage.error('保存套餐失败');
    } finally {
      submitLoading.value = false;
    }
  });
};

// 删除套餐
const handleDelete = (row: Package) => {
  ElMessageBox.confirm(`确定要删除套餐 "${row.title}" 吗？`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      try {
        if (row.id) {
          await deletePackage(row.id);
          ElMessage.success('删除成功');
          getPackageData(); // 刷新列表
        } else {
          ElMessage.error('套餐ID不存在，无法删除');
        }
      } catch (error) {
        console.error('删除套餐失败', error);
        ElMessage.error('删除套餐失败');
      }
    })
    .catch(() => {
      // 取消删除
    });
};

// 处理日期变化
const handleDateChange = (value: [Date, Date] | null) => {
  if (value) {
    // 当日期选择器值变化时，立即更新表单中的开始日期和结束日期
    packageForm.start_date = value[0].toISOString();
    packageForm.end_date = value[1].toISOString();
  } else {
    // 清空日期
    packageForm.start_date = '';
    packageForm.end_date = '';
  }
};
</script>

<style scoped>
.package-container {
  padding: 20px;
}

.search-card {
  margin-bottom: 20px;
}

.action-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.table-card {
  margin-top: 20px;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}
</style>
