<template>
  <div
    class="app-wrapper"
    :class="{ 'is-collapsed': isCollapsed }"
  >
    <!-- 侧边栏 -->
    <Sidebar
      class="sidebar-container"
      :is-collapsed="isCollapsed"
    />

    <!-- 主容器 -->
    <div class="main-container">
      <!-- 头部 -->
      <Navbar
        :toggle-sidebar="toggleSidebar"
        :is-collapsed="isCollapsed"
      />

      <!-- 标签导航 -->
      <TagsView v-if="showTagsView" />

      <!-- 内容区域 -->
      <AppMain />

      <!-- 底部版权 -->
      <Footer v-if="showFooter" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import Sidebar from './components/Sidebar.vue';
import Navbar from './components/Navbar.vue';
import AppMain from './components/AppMain.vue';
import TagsView from './components/TagsView.vue';
import Footer from './components/Footer.vue';

// 是否显示标签导航和底部版权，可以从配置或状态管理中获取
const showTagsView = ref(true);
const showFooter = ref(true);

// 侧边栏折叠状态
const isCollapsed = ref(false);

// 切换侧边栏折叠状态
const toggleSidebar = () => {
  isCollapsed.value = !isCollapsed.value;
};
</script>

<style scoped>
.app-wrapper {
  position: relative;
  height: 100vh;
  width: 100%;
  display: flex;
  overflow: hidden;
  background-color: #f5f7fa;
}

.app-wrapper.is-collapsed .sidebar-container {
  width: 64px;
}

.sidebar-container {
  transition: width 0.3s;
  z-index: 1001;
  box-shadow: 2px 0 6px rgba(0, 21, 41, 0.15);
}

.main-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-height: 100%;
  position: relative;
  transition: margin-left 0.3s;
}
</style>
