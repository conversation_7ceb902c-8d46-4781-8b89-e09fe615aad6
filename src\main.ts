import { createApp } from 'vue';
import App from './App.vue';
import router from './router';
import ElementPlus from 'element-plus';
import 'element-plus/dist/index.css';
import * as ElementPlusIconsVue from '@element-plus/icons-vue';
import pinia from './store';
import './style.css';

// 导入中文语言包
import zhCn from 'element-plus/dist/locale/zh-cn.mjs';

// 输出当前环境信息
console.log('当前环境：', import.meta.env.VITE_APP_MODE || 'development');
console.log('API地址：', import.meta.env.VITE_APP_BASE_API);
console.log('是否启用Mock：', import.meta.env.VITE_APP_USE_MOCK);

// 导入mock数据，根据环境变量决定是否启用
if (import.meta.env.VITE_APP_USE_MOCK === 'true') {
  import('./mock');
}

const app = createApp(App);

// 全局错误处理
app.config.errorHandler = (err, instance, info) => {
  console.error('Vue错误:', err);
  console.error('错误来源:', info);
};

// 未捕获的Promise异常处理
window.addEventListener('unhandledrejection', event => {
  console.error('未处理的Promise异常:', event.reason);
});

// 注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component);
}

// 使用 Element Plus 并设置中文语言
app.use(ElementPlus, {
  locale: zhCn,
});

app.use(router);
app.use(pinia);

// 检查localStorage中是否有用户信息
const storedUserInfo = localStorage.getItem('userInfo');
console.log('应用启动时的用户信息:', storedUserInfo);

app.mount('#app');
