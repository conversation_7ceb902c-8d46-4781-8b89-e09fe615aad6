declare module 'mockjs' {
  export interface MockjsRandom {
    id(): never;
    guid(): string;
    natural(min?: number, max?: number): number;
    integer(min?: number, max?: number): number;
    float(min?: number, max?: number, dmin?: number, dmax?: number): number;
    character(pool?: string): string;
    string(pool?: string, min?: number, max?: number): string;
    range(start: number, stop: number, step?: number): number[];
    date(format?: string): string;
    time(format?: string): string;
    datetime(format?: string): string;
    now(unit?: string, format?: string): string;
    image(
      size?: string,
      background?: string,
      foreground?: string,
      format?: string,
      text?: string
    ): string;
    color(): string;
    hex(): string;
    rgb(): string;
    rgba(): string;
    hsl(): string;
    paragraph(min?: number, max?: number): string;
    cparagraph(min?: number, max?: number): string;
    sentence(min?: number, max?: number): string;
    csentence(min?: number, max?: number): string;
    word(min?: number, max?: number): string;
    cword(pool?: string | string[], min?: number, max?: number): string;
    title(min?: number, max?: number): string;
    ctitle(min?: number, max?: number): string;
    first(): string;
    last(): string;
    name(middle?: boolean): string;
    cfirst(): string;
    clast(): string;
    cname(): string;
    url(protocol?: string, host?: string): string;
    protocol(): string;
    domain(tld?: string): string;
    tld(): string;
    email(domain?: string): string;
    ip(): string;
    region(): string;
    province(): string;
    city(prefix?: boolean): string;
    county(prefix?: boolean): string;
    zip(len?: number): string;
    capitalize(word: string): string;
    upper(str: string): string;
    lower(str: string): string;
    pick<T>(arr: T[]): T;
    shuffle<T>(arr: T[]): T[];
    boolean(min?: number, max?: number, current?: boolean): boolean;
  }

  export interface Mockjs {
    Random: MockjsRandom;
    mock<T>(template: T): T;
    mock(rurl: RegExp | string, rtype: string, template: any): Mockjs;
    mock(rurl: RegExp | string, template: any): Mockjs;
    setup(settings: { timeout?: string | number }): void;
  }

  const Mock: Mockjs;
  export default Mock;
}
