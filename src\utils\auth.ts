// 对密码进行加密
import CryptoJS from 'crypto-js';

const TOKEN_KEY = 'token';
const USER_INFO_KEY = 'userInfo';

export const encryptPassword = (password: string) => {
  return CryptoJS.SHA256(password).toString();
};

// 保存token
export const saveToken = (token: string) => {
  localStorage.setItem(TOKEN_KEY, token);
};

// 获取token
export const getToken = () => {
  return localStorage.getItem(TOKEN_KEY);
};

// 删除token
export const removeToken = () => {
  localStorage.removeItem(TOKEN_KEY);
};

// 保存用户信息
export const saveUserInfo = (userInfo: any) => {
  localStorage.setItem(USER_INFO_KEY, JSON.stringify(userInfo));
};

// 获取用户信息
export const getUserInfo = () => {
  return localStorage.getItem(USER_INFO_KEY);
};

// 删除用户信息
export const removeUserInfo = () => {
  localStorage.removeItem(USER_INFO_KEY);
};
