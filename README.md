# 管理系统项目

## 项目概述
一个基于Vue 3、Element Plus的后台管理系统，遵循RESTful API规范。

## 技术栈
- 前端框架：Vue 3
- UI库：Element Plus 
- 状态管理：Pinia
- 路由：Vue Router
- HTTP客户端：Axios
- 包管理器：pnpm

## 项目结构
```
src/
├── api/                # API接口目录
│   ├── system/         # 系统相关API
│   │   └── user.ts     # 用户相关API
│   ├── plugin/         # 插件相关API
│   ├── types.ts        # API类型定义
│   ├── payment.ts      # 支付相关API
│   └── dashboard.ts    # 仪表盘相关API
├── assets/             # 静态资源
├── components/         # 公共组件
├── layout/             # 布局组件
├── mock/               # 模拟数据
├── router/             # 路由配置
├── store/              # 状态管理
├── utils/              # 工具函数
│   ├── request.ts      # Axios请求封装
│   └── auth.ts         # 认证相关工具
├── views/              # 页面视图
│   └── system/         # 系统管理模块
│       └── user/       # 用户管理页面
├── App.vue             # 根组件
├── env.d.ts            # 环境变量类型声明
├── main.ts             # 入口文件
└── style.css           # 全局样式
```

## RESTful API 规范
本项目严格遵循RESTful API设计规范：

1. 资源命名：使用名词复数形式（如 `/api/users`）
2. HTTP方法：
   - GET：获取资源
   - POST：创建资源
   - PUT：更新资源（完整更新）
   - PATCH：部分更新资源
   - DELETE：删除资源
3. 状态码使用：
   - 0：成功
   - 400：客户端错误
   - 401：未授权
   - 403：禁止访问
   - 404：资源不存在
   - 500：服务器错误
4. 统一响应格式：
   ```json
   {
     "code": 0,  // 0表示成功，非0表示错误
     "msg": "操作成功",
     "data": {}  // 响应数据
   }
   ```
   **注意：** 本项目中API响应状态码以`code`字段为准，`code=0`表示请求成功，其他值表示错误。
   拦截器已经拦截code !== 0的响应，并且直接返回了data

## 开发指南
1. 安装依赖：`pnpm install`
2. 本地开发：`pnpm dev`
3. 构建项目：`pnpm build`

## 代码规范
- 组件命名：使用PascalCase
- 文件命名：使用kebab-case
- API接口：集中管理在api目录
- 类型定义：使用TypeScript接口定义数据结构