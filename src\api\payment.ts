import request from '@/utils/request';
import { downloadFile } from '@/utils/request';

// 支付明细数据接口
export interface PaymentDetail {
  id: number;
  orderNo: string;
  paymentNo: string;
  amount: number;
  paymentMethod: string;
  status: string;
  userName: string;
  userPhone?: string;
  createTime: string;
  payTime?: string;
}

// 获取支付明细列表
export function getPaymentList(params: any) {
  return request<{ list: PaymentDetail[]; total: number }>({
    url: '/api/v1/order',
    method: 'get',
    params,
  });
}

// 获取支付明细详情
export function delPaymentDetail(id: number) {
  return request<PaymentDetail>({
    url: `/api/payment/detail/${id}`,
    method: 'delete',
  });
}

/**
 * 导出支付明细列表
 * @param params 查询参数，与获取列表接口参数一致
 * @returns Promise<void>
 */
export function exportPaymentList(params: any): Promise<void> {
  return downloadFile(
    {
      url: '/api/v1/order/export',
      method: 'get',
      params,
    },
    '支付明细导出.csv'
  );
}
