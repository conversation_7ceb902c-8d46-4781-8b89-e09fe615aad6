<template>
  <div
    class="sidebar-container"
    :class="{ 'is-collapsed': isCollapsed }"
  >
    <div class="logo-container">
      <h1
        v-if="!isCollapsed"
        class="logo-title"
      >
        My Admin
      </h1>
      <h1
        v-else
        class="logo-title-collapsed"
      >
        MA
      </h1>
    </div>
    <el-menu
      :default-active="activeMenu"
      class="sidebar-menu"
      background-color="#304156"
      text-color="#bfcbd9"
      active-text-color="#409EFF"
      :collapse="isCollapsed"
      router
    >
      <!-- 动态渲染所有菜单 -->
      <template
        v-for="menuRoute in menuRoutes"
        :key="menuRoute.path"
      >
        <!-- 带子菜单的路由 -->
        <el-sub-menu
          v-if="menuRoute.children && menuRoute.children.length > 0"
          :index="menuRoute.path"
        >
          <template #title>
            <el-icon>
              <component :is="getIcon(menuRoute.meta?.icon)" />
            </el-icon>
            <span>{{ menuRoute.meta?.title || menuRoute.name }}</span>
          </template>

          <!-- 渲染子路由 -->
          <el-menu-item
            v-for="child in menuRoute.children"
            v-show="!child.meta?.hidden"
            :key="child.path"
            :index="child.path"
          >
            <el-icon>
              <component :is="getIcon(child.meta?.icon)" />
            </el-icon>
            <template #title>
              {{ child.meta?.title || child.name }}
            </template>
          </el-menu-item>
        </el-sub-menu>

        <!-- 没有子菜单的路由 -->
        <el-menu-item
          v-else
          v-show="!menuRoute.meta?.hidden"
          :index="menuRoute.path"
        >
          <el-icon>
            <component :is="getIcon(menuRoute.meta?.icon)" />
          </el-icon>
          <template #title>
            {{ menuRoute.meta?.title || menuRoute.name }}
          </template>
        </el-menu-item>
      </template>
    </el-menu>
  </div>
</template>

<script setup lang="ts">
import { computed, markRaw, toRefs, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useUserStore } from '@/store/user';
import * as ElementPlusIconsVue from '@element-plus/icons-vue';

// 预处理所有图标并标记为非响应式
const icons: Record<string, any> = {};

// 导入所有Element Plus图标并处理名称格式
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  // 小写键名，用于匹配小写的图标名称
  icons[key.toLowerCase()] = markRaw(component);
  // 原始键名，用于匹配原始的图标名称
  icons[key] = markRaw(component);
}

const route = useRoute();
const router = useRouter();
const userStore = useUserStore();

// 获取图标组件
const getIcon = (iconName: any) => {
  if (!iconName) return icons['document'] || icons['Document'];

  // 尝试直接匹配
  if (icons[iconName]) {
    return icons[iconName];
  }

  // 尝试小写匹配
  if (icons[iconName.toLowerCase()]) {
    return icons[iconName.toLowerCase()];
  }

  // 尝试首字母大写匹配
  const capitalizedName = iconName.charAt(0).toUpperCase() + iconName.slice(1);
  if (icons[capitalizedName]) {
    return icons[capitalizedName];
  }

  // 如果都不匹配，返回默认图标
  return icons['document'] || icons['Document'];
};

// 判断用户是否有权限
// const checkPermission = (_permissions: any) => {
//   // 移除权限检查，始终返回true
//   return true
// }

// 接收父组件传递的折叠状态
const props = defineProps({
  isCollapsed: {
    type: Boolean,
    default: false,
  },
});

// 解构props以使用isCollapsed
const { isCollapsed } = toRefs(props);

// 获取菜单路由
const menuRoutes = computed(() => {
  // 获取静态路由
  const routes = router.getRoutes();
  const layoutRoute = routes.find(r => r.name === 'Layout');
  const staticRoutes = layoutRoute
    ? layoutRoute.children.filter(route => {
        return !route.meta?.hidden;
      })
    : [];

  // 合并静态路由和用户store中的侧边栏路由
  // console.log('静态路由:', staticRoutes);
  // console.log('Store侧边栏路由:', userStore.sidebarRoutes);

  return [...staticRoutes, ...userStore.sidebarRoutes];
});

// 当前激活的菜单
const activeMenu = computed(() => {
  // 如果路由有设置activeMenu，则使用activeMenu，否则使用当前路径
  return (route.meta.activeMenu as string) || route.path;
});

// 组件挂载时获取菜单数据
onMounted(() => {
  userStore.getUserMenu();
});
</script>

<style scoped>
.sidebar-container {
  width: 210px;
  height: 100%;
  background-color: #304156;
  transition: width 0.28s;
  overflow-y: auto;
}

.sidebar-container.is-collapsed {
  width: 64px;
}

.logo-container {
  height: 50px;
  padding: 10px 0;
  text-align: center;
  overflow: hidden;
}

.logo-title {
  color: #fff;
  font-size: 18px;
  margin: 0;
  font-weight: 600;
  white-space: nowrap;
}

.logo-title-collapsed {
  color: #fff;
  font-size: 18px;
  margin: 0;
  font-weight: 600;
}

.sidebar-menu {
  border-right: none;
}

.sidebar-menu :deep(.el-sub-menu__title) {
  color: #bfcbd9;
}

.sidebar-menu :deep(.el-sub-menu__title:hover) {
  background-color: #263445;
}

.sidebar-menu :deep(.el-menu-item) {
  color: #bfcbd9;
}

.sidebar-menu :deep(.el-menu-item:hover) {
  background-color: #263445;
}

.sidebar-menu :deep(.el-menu-item.is-active) {
  color: #409eff;
  background-color: #263445;
}

.sidebar-container :deep(.el-menu--collapse) {
  width: 64px;
}
</style>
