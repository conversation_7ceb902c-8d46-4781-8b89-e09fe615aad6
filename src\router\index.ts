import { createRouter, createWebHashHistory, RouteRecordRaw } from 'vue-router';
import { useUserStore } from '../store/user';

// 基础路由
const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    name: 'Layout',
    component: () => import('@/layout/index.vue'),
    redirect: '/home',
    children: [
      {
        path: '/home',
        name: 'Home',
        component: () => import('@/views/home/<USER>'),
        meta: {
          title: '首页',
          icon: 'home',
        },
      },
    ],
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/login/index.vue'),
    meta: {
      title: '登录',
    },
  },
  {
    path: '/404',
    name: 'NotFound',
    component: () => import('@/views/404/index.vue'),
    meta: {
      title: '404',
    },
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404',
  },
];

const router = createRouter({
  history: createWebHashHistory(),
  routes,
});

// 全局前置守卫
router.beforeEach((to, from, next) => {
  console.log(to);

  // 设置标题
  document.title = (to.meta.title as string) || 'My Admin';

  // 判断是否登录
  const token = localStorage.getItem('token');

  // 每次路由跳转都尝试初始化用户信息
  if (token && to.path !== '/login') {
    const userStore = useUserStore();
    // 如果用户信息为空，尝试从localStorage加载
    if (!userStore.userInfo || Object.keys(userStore.userInfo).length === 0) {
      userStore.initUserInfo();
    }
    if (userStore.allRoutes.length === 0) {
      userStore.getUserMenu().then((res: any) => {
        res.forEach((route: any) => {
          router.addRoute('Layout', route);
        });

        next({ path: to.redirectedFrom?.fullPath });
      });
    } else {
      next();
    }
  } else if (to.path !== '/login' && !token) {
    // 使用 replace 而不是 push 来避免历史记录堆积
    next({ path: '/login', replace: true });
  } else {
    console.log('跳转', router.getRoutes());

    next();
  }
});

export default router;
