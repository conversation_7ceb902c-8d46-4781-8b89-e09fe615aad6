import request from '@/utils/request';

// 角色表单数据
export interface RoleForm {
  id?: string;
  name: string;
  des?: string;
}

// 角色查询参数
export interface RoleQuery {
  name?: string;
}

// 角色信息
export interface RoleInfo {
  id: string;
  name: string;
  des?: string;
  CreatedAt?: string;
  UpdatedAt?: string;
}

// 分配菜单参数
export interface AssignMenuParams {
  role_id: string;
  menu_ids: string; // 格式为"1,2,3,4,5"
}

/**
 * 获取角色列表
 */
export function getRoleList(): Promise<RoleInfo[]> {
  return request({
    url: '/api/v1/role',
    method: 'get',
  });
}

/**
 * 新增角色
 */
export function addRole(data: RoleForm): Promise<RoleInfo> {
  return request({
    url: '/api/v1/role',
    method: 'post',
    data,
  });
}

/**
 * 修改角色
 */
export function updateRole(id: string, data: RoleForm): Promise<RoleInfo> {
  return request({
    url: `/api/v1/role/${id}`,
    method: 'put',
    data,
  });
}

/**
 * 删除角色
 */
export function deleteRole(id: string): Promise<null> {
  return request({
    url: `/api/v1/role/${id}`,
    method: 'delete',
  });
}

/**
 * 分配菜单权限
 */
export function assignRoleMenu(data: AssignMenuParams): Promise<null> {
  return request({
    url: '/api/v1/role/menu',
    method: 'post',
    data,
  });
}

/**
 * 获取角色菜单权限
 * @param id 角色ID
 * @returns 返回菜单ID字符串，格式为"1,2,3,4,5"
 */
export function getRoleMenu(id: string): Promise<any> {
  return request({
    url: `/api/v1/role/menu/${id}`,
    method: 'get',
  });
}

/**
 * 获取所有角色列表
 */
export function getAllRoleList(): Promise<RoleInfo[]> {
  return request({
    url: '/api/v1/role',
    method: 'get',
  });
}
