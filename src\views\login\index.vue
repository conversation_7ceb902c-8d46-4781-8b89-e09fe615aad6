<template>
  <div class="login-container">
    <div class="login-background">
      <div class="login-blob" />
    </div>

    <div class="login-panel">
      <div class="left-panel">
        <div class="title-container">
          <h2 class="title">My Admin</h2>
          <p class="subtitle">专业的管理系统解决方案</p>
        </div>
        <div class="feature-list">
          <div class="feature-item">
            <el-icon class="feature-icon">
              <Monitor />
            </el-icon>
            <div class="feature-content">
              <h3>现代化界面</h3>
              <p>采用最新的设计理念，提供流畅的用户体验</p>
            </div>
          </div>
          <div class="feature-item">
            <el-icon class="feature-icon">
              <Operation />
            </el-icon>
            <div class="feature-content">
              <h3>高效管理</h3>
              <p>强大的功能模块，优化您的工作流程</p>
            </div>
          </div>
          <div class="feature-item">
            <el-icon class="feature-icon">
              <Lock />
            </el-icon>
            <div class="feature-content">
              <h3>安全可靠</h3>
              <p>多重安全防护，保障您的数据安全</p>
            </div>
          </div>
        </div>
      </div>

      <div class="login-form-container">
        <div class="login-form-header">
          <h3 class="login-form-title">用户登录</h3>
          <p class="login-form-subtitle">欢迎回来，请登录您的账号</p>
        </div>

        <el-form
          ref="loginFormRef"
          :model="loginForm"
          :rules="loginRules"
          class="login-form"
        >
          <el-form-item prop="username">
            <el-input
              v-model="loginForm.username"
              placeholder="请输入用户名"
              :prefix-icon="User"
              clearable
              autofocus
            />
          </el-form-item>

          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              type="password"
              placeholder="请输入密码"
              :prefix-icon="Lock"
              show-password
              @keyup.enter="handleLogin"
            />
          </el-form-item>

          <div class="remember-container">
            <el-checkbox v-model="rememberMe">记住我</el-checkbox>
            <el-link
              type="primary"
              :underline="false"
            >
              忘记密码?
            </el-link>
          </div>

          <el-form-item>
            <el-button
              :loading="loading"
              type="primary"
              class="login-button"
              @click="handleLogin"
            >
              登 录
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { User, Lock, Monitor, Operation } from '@element-plus/icons-vue';
import { login } from '@/api/system/user';
import { saveToken, saveUserInfo } from '@/utils/auth';
import { encryptPassword } from '@/utils/auth';

const router = useRouter();
const loginFormRef = ref();
const loading = ref(false);
const rememberMe = ref(false);

// 登录表单
const loginForm = reactive({
  username: '',
  password: '',
});

// 表单验证规则
const loginRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    // { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' },
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    // { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' },
  ],
};

// 处理登录
const handleLogin = () => {
  loginFormRef.value.validate(async (valid: boolean) => {
    if (valid) {
      loading.value = true;

      try {
        const res: any = await login({
          account: loginForm.username,
          password: encryptPassword(loginForm.password),
        });
        console.log(res);
        saveToken(res.token);
        saveUserInfo({
          name: loginForm.username,
          avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
        });
        router.push('/');
        ElMessage.success('登录成功');
      } catch {
        ElMessage.error('登录失败');
      } finally {
        loading.value = false;
      }

      // 实际项目中使用API登录
      // login(loginForm)
      //   .then(res => {
      //     console.log('登录成功，用户信息:', res);
      //     saveToken(res.data.token);
      //     saveUserInfo(res.data.userInfo);
      //     console.log('登录成功后存储的用户信息:', userStore.userInfo);

      //     // 等待动态路由加载完成后再跳转
      //     setTimeout(() => {
      //       router.push('/');
      //       ElMessage.success('登录成功');
      //     }, 300);
      //   })
      //   .catch(() => {
      //     // 模拟登录成功，实际开发时请删除
      //     if (loginForm.username === 'admin' && loginForm.password === '123456') {
      //       const mockToken = 'mock-token-' + Date.now();
      //       const mockUserInfo = {
      //         id: 1,
      //         username: 'admin',
      //         name: '管理员',
      //         avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
      //       };

      //       // 先设置token再设置用户信息
      //       saveToken(mockToken);
      //       saveUserInfo(mockUserInfo);

      //       // 添加延迟，确保动态路由加载完成
      //       setTimeout(() => {
      //         console.log('模拟登录成功，用户信息:', userStore.userInfo);
      //         router.push('/');
      //         ElMessage.success('登录成功');
      //       }, 300);
      //     } else {
      //       ElMessage.error('用户名或密码错误');
      //     }
      //   })
      //   .finally(() => {
      //     loading.value = false;
      //   });
    }
  });
};
</script>

<style scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  width: 100vw;
  position: relative;
  overflow: hidden;
  background-color: #f0f2f5;
}

.login-background {
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 0;
}

.login-blob {
  position: absolute;
  width: 800px;
  height: 800px;
  background: linear-gradient(135deg, #1890ff 0%, #67c23a 100%);
  border-radius: 50%;
  filter: blur(80px);
  opacity: 0.2;
  top: -200px;
  left: -200px;
  z-index: -1;
}

.login-panel {
  display: flex;
  width: 900px;
  height: 560px;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  z-index: 1;
  overflow: hidden;
}

.left-panel {
  width: 45%;
  background: linear-gradient(135deg, #1890ff 0%, #36cfc9 100%);
  color: white;
  padding: 40px;
  display: flex;
  flex-direction: column;
}

.title-container {
  margin-bottom: 40px;
}

.title {
  font-size: 28px;
  font-weight: bold;
  margin: 0 0 10px 0;
}

.subtitle {
  font-size: 16px;
  opacity: 0.8;
  margin: 0;
}

.feature-list {
  margin-top: 40px;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 30px;
}

.feature-icon {
  font-size: 28px;
  margin-right: 15px;
  margin-top: 5px;
}

.feature-content h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
}

.feature-content p {
  margin: 0;
  opacity: 0.8;
  font-size: 14px;
}

.login-form-container {
  width: 55%;
  padding: 40px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.login-form-header {
  margin-bottom: 30px;
  text-align: center;
}

.login-form-title {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin: 0 0 10px 0;
}

.login-form-subtitle {
  font-size: 14px;
  color: #909399;
  margin: 0;
}

.login-form {
  width: 100%;
}

.remember-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.login-button {
  width: 100%;
  height: 44px;
  font-size: 16px;
}

.login-tips {
  margin-top: 20px;
  color: #909399;
  font-size: 12px;
  text-align: center;
}

.login-tips p {
  margin: 5px 0;
}

@media (max-width: 768px) {
  .login-panel {
    width: 95%;
    flex-direction: column;
    height: auto;
  }

  .left-panel {
    width: 100%;
    padding: 20px;
  }

  .login-form-container {
    width: 100%;
    padding: 20px;
  }

  .title {
    font-size: 24px;
  }

  .feature-list {
    display: none;
  }
}
</style>
