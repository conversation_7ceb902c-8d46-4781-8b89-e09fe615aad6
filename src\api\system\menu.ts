import request from '@/utils/request';

/**
 * 菜单项接口定义
 */
export interface MenuItem {
  id: number;
  parent_id?: number;
  name: string;
  icon?: string;
  path: string;
  group_path: string;
  sort: number;
  is_show: number;
  children?: MenuItem[] | null;
  CreatedAt?: string;
  UpdatedAt?: string;
}

/**
 * 获取所有菜单
 */
export function getMenuList(): Promise<MenuItem[]> {
  return request({
    url: '/api/v1/menu',
    method: 'get',
  });
}

/**
 * 新增菜单
 * @param data 菜单数据
 */
export function addMenu(data: Omit<MenuItem, 'id'>): Promise<MenuItem> {
  return request({
    url: '/api/v1/menu',
    method: 'post',
    data,
  });
}

/**
 * 更新菜单
 * @param id 菜单ID
 * @param data 菜单数据
 */
export function updateMenu(id: number, data: Partial<Omit<MenuItem, 'id'>>): Promise<MenuItem> {
  return request({
    url: `/api/v1/menu/${id}`,
    method: 'put',
    data,
  });
}

/**
 * 删除菜单
 * @param id 菜单ID
 */
export function deleteMenu(id: number): Promise<any> {
  return request({
    url: `/api/v1/menu/${id}`,
    method: 'delete',
  });
}
