import request from '@/utils/request';

// 用户数据接口
export interface User {
  id?: number;
  nick_name?: string;
  tell?: string;
  login_ip?: string | null;
  login_at?: string | null;
  created_at?: string;
  updated_at?: string;
  user_package?: {
    EndTime: string;
  };
}

// 登录参数接口
export interface LoginParams {
  username: string;
  password: string;
}

// 新增用户参数接口
export interface AddUserParams {
  tell?: string; // 手机号
  nick_name?: string; // 昵称
}

// 登录记录接口
export interface LoginRecord {
  uid: number;
  ip: string;
  created_at: string;
  updated_at: string;
}

// 登录记录列表响应接口
export interface LoginRecordListResponse {
  list: LoginRecord[];
  total: number;
}

// 获取用户列表
export function getUserList(params: any) {
  return request<{ list: User[]; total: number }>({
    url: '/api/v1/user/',
    method: 'get',
    params,
  });
}

// 添加用户 - RESTful规范
export function addUser(data: AddUserParams) {
  return request<User>({
    url: '/api/v1/user/',
    method: 'post',
    data,
  });
}

// 更新用户
export function updateUser(data: Partial<User>) {
  return request<User>({
    url: `/api/v1/user/${data.id}`,
    method: 'put',
    data,
  });
}

// 删除用户
export function deleteUser(id: number) {
  return request<null>({
    url: `/api/v1/user/${id}`,
    method: 'delete',
  });
}

// 批量删除用户
export function batchDeleteUsers(ids: number[]) {
  return request<null>({
    url: '/api/user/batchDelete',
    method: 'delete',
    data: { ids },
  });
}

// 开通套餐
export function openPackage(data: { uid: number; package_id: number }) {
  return request<null>({
    url: '/api/v1/user/open/package',
    method: 'post',
    data,
  });
}

// 获取用户登录记录
export function getUserLoginRecords(uid: number, params: { page: number; page_size: number }) {
  return request<LoginRecordListResponse>({
    url: `/api/v1/user/login/record/${uid}`,
    method: 'get',
    params,
  });
}

// 下线用户
export function logoutUser(id: number) {
  return request<null>({
    url: '/api/v1/user/login/out',
    method: 'post',
    data: { id },
  });
}
