<template>
  <div class="main-container">
    <!-- 搜索和筛选 -->
    <el-card class="search-card">
      <el-form
        :model="searchForm"
        label-width="60px"
        inline
      >
        <el-form-item label="账号">
          <el-input
            v-model="searchForm.account"
            placeholder="请输入账号"
            clearable
          />
        </el-form-item>
        <el-form-item label="手机号">
          <el-input
            v-model="searchForm.tell"
            placeholder="请输入手机号"
            clearable
          />
        </el-form-item>
        <el-form-item label="部门">
          <el-cascader
            v-model="searchForm.department_id"
            :options="departmentOptions"
            :props="departmentProps"
            placeholder="请选择部门"
            clearable
          />
        </el-form-item>
        <el-form-item label="岗位">
          <div style="width: 140px">
            <el-select
              v-model="searchForm.post_id"
              placeholder="请选择岗位"
              clearable
            >
              <el-option
                v-for="item in positionOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </div>
        </el-form-item>
        <el-form-item label="角色">
          <div style="width: 140px">
            <el-select
              v-model="searchForm.role_id"
              placeholder="请选择角色"
              clearable
            >
              <el-option
                v-for="item in roleOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </div>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            @click="handleSearch"
          >
            搜索
          </el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>

      <div class="action-header">
        <el-button
          type="primary"
          :icon="Plus"
          @click="handleAddPackage"
        >
          新增用户
        </el-button>
      </div>
    </el-card>

    <div class="user-container">
      <div class="role-container">
        <DepartmentPage @filter-department="handleFilterByDepartment" />
      </div>
      <!-- 表格 -->
      <el-card class="table-card">
        <el-table
          :data="tableData"
          style="width: 100%"
          height="460"
          border
        >
          <!-- id -->
          <el-table-column
            prop="id"
            label="ID"
            fixed
          />
          <!-- 账号 -->
          <el-table-column
            prop="account"
            label="账号"
          />
          <!-- 手机号 -->
          <el-table-column
            prop="tell"
            label="手机号"
            width="120"
          />
          <!-- 部门 -->
          <el-table-column
            prop="Department.name"
            label="部门"
            width="120"
          />
          <!-- 职位 -->
          <el-table-column
            prop="Post.name"
            label="职位"
            width="120"
          />
          <!-- 角色 -->
          <el-table-column
            prop="Role.name"
            label="角色"
            width="120"
          />
          <!-- 状态 -->
          <el-table-column
            prop="status"
            label="状态"
          >
            <template #default="scope">
              <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
                {{ scope.row.status === 1 ? '正常' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>
          <!-- ip -->
          <el-table-column
            prop="last_ip"
            label="IP"
          />
          <!-- 创建时间 -->
          <el-table-column
            prop="created_at"
            label="创建时间"
            width="180"
          >
            <template #default="scope">
              {{ formatDate(scope.row.created_at) }}
            </template>
          </el-table-column>
          <!-- 更新时间 -->
          <el-table-column
            prop="updated_at"
            label="更新时间"
            width="180"
          >
            <template #default="scope">
              {{ formatDate(scope.row.updated_at) }}
            </template>
          </el-table-column>
          <!-- 操作 -->
          <el-table-column
            label="操作"
            fixed="right"
            width="240"
          >
            <template #default="scope">
              <el-button
                type="primary"
                size="small"
                @click="handleEdit(scope.row)"
              >
                编辑
              </el-button>
              <el-button
                type="danger"
                size="small"
                @click="handleDelete(scope.row)"
              >
                删除
              </el-button>
              <el-button
                type="primary"
                size="small"
                @click="copyInviteLink(scope.row)"
              >
                邀请链接
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-container">
          <!-- 分页 -->
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 30, 40]"
            :background="background"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>

      <!-- 新增用户对话框 -->
      <el-dialog
        v-model="dialogVisible"
        title="新增用户"
        width="50%"
      >
        <el-form
          ref="formRef"
          :model="form"
          label-width="120px"
          :rules="formRules"
        >
          <el-form-item
            label="账号"
            prop="account"
          >
            <el-input v-model="form.account" />
          </el-form-item>
          <el-form-item
            label="密码"
            prop="password"
          >
            <el-input
              v-model="form.password"
              show-password
            />
          </el-form-item>
          <el-form-item
            label="手机号"
            prop="tell"
          >
            <el-input v-model="form.tell" />
          </el-form-item>
          <el-form-item
            label="部门"
            prop="department_id"
          >
            <el-cascader
              v-model="form.department_id"
              :options="departmentOptions"
              :props="departmentProps"
              placeholder="请选择部门"
              clearable
            />
          </el-form-item>
          <el-form-item
            label="岗位"
            prop="post_id"
          >
            <el-select
              v-model="form.post_id"
              placeholder="请选择岗位"
            >
              <el-option
                v-for="item in positionOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            label="角色"
            prop="role_id"
          >
            <el-select
              v-model="form.role_id"
              placeholder="请选择角色"
            >
              <el-option
                v-for="item in roleOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            v-if="dialogType === 'edit'"
            label="状态"
          >
            <!-- 切换按钮 -->
            <el-switch v-model="form.status" />
          </el-form-item>
        </el-form>
        <template #footer>
          <el-button
            type="default"
            @click="dialogVisible = false"
          >
            取消
          </el-button>
          <el-button
            type="primary"
            @click="handleSubmit"
          >
            确定
          </el-button>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script setup lang="ts">
/**
 * 用户管理组件
 * 提供用户列表展示、新增、编辑、删除等功能
 * 遵循RESTful API规范，与后端交互
 * @component UserManagement
 */
import { Plus } from '@element-plus/icons-vue';
import { reactive, ref, onMounted } from 'vue';
import type { FormRules, FormInstance } from 'element-plus';
import { addUser, getUserList, deleteUser, updateUser } from '@/api/system/user';
import { ElMessage } from 'element-plus';
import { encryptPassword } from '@/utils/auth';
import type { User } from '@/api/system/user';
import { formatDate } from '@/utils/tools';
import DepartmentPage from '../department/index.vue';
// 导入部门、岗位和角色相关API
import { getDepartmentTree, type Department } from '@/api/system/department';
import { getAllPositions, type Position } from '@/api/system/position';
import { getAllRoleList, type RoleInfo } from '@/api/system/role';

// 部门级联选择器配置
const departmentProps = {
  value: 'id',
  label: 'name',
  children: 'children',
  checkStrictly: true,
  emitPath: false,
};

// 部门、岗位和角色下拉选项
const departmentOptions = ref<Department[]>([]);
const positionOptions = ref<Position[]>([]);
const roleOptions = ref<RoleInfo[]>([]);

// 获取部门列表
const getDepartmentList = async () => {
  try {
    const res = await getDepartmentTree();
    departmentOptions.value = res.filter(item => item.id !== 0);
  } catch (error) {
    console.error('获取部门列表失败', error);
    ElMessage.error('获取部门列表失败');
  }
};

// 获取岗位列表
const getPositionList = async () => {
  try {
    const res = await getAllPositions();
    positionOptions.value = res as Position[];
  } catch (error) {
    console.error('获取岗位列表失败', error);
    ElMessage.error('获取岗位列表失败');
  }
};

// 获取角色列表
const getRoleList = async () => {
  try {
    const res = await getAllRoleList();
    roleOptions.value = res;
  } catch (error) {
    console.error('获取角色列表失败', error);
    ElMessage.error('获取角色列表失败');
  }
};

/**
 * 处理新增用户按钮点击
 * 打开新增用户对话框
 */
const handleAddPackage = () => {
  console.log('新增用户');
  dialogType.value = 'add';
  dialogVisible.value = true;
  // 初始化表单
  form.value = {
    id: null,
    account: '',
    password: '',
    tell: '',
    department_id: undefined,
    post_id: undefined,
    role_id: undefined,
    status: true,
  };
};

/**
 * 处理编辑用户按钮点击
 * 打开编辑用户对话框并填充数据
 * @param {any} row - 用户行数据
 */
const handleEdit = (row: any) => {
  console.log('编辑用户', row);
  // 填充表单数据
  form.value = {
    id: row.id,
    account: row.account,
    password: '',
    tell: row.tell,
    department_id: row.department_id,
    post_id: row.post_id,
    role_id: row.role_id,
    status: row.status === 1,
  };
  dialogType.value = 'edit';
  dialogVisible.value = true;
};

/**
 * 处理删除用户按钮点击
 * @param {any} row - 用户行数据
 */
const handleDelete = async (row: any) => {
  console.log('删除用户', row);
  try {
    await deleteUser(row.id);
    getUserListData();
    ElMessage.success('删除用户成功');
  } catch {
    ElMessage.error('删除用户失败');
  }
};

// 表格数据
const tableData = ref<User[]>([]);

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const background = ref(true);

// 搜索表单
const searchForm = ref({
  account: '',
  tell: '',
  department_id: undefined as number | undefined,
  post_id: undefined as number | undefined,
  role_id: undefined as string | undefined,
});

/**
 * 获取用户列表
 */
const getUserListData = async () => {
  const params = {
    page: currentPage.value,
    page_size: pageSize.value,
    ...searchForm.value,
  };

  // 移除空值
  Object.keys(params).forEach(key => {
    if (params[key] === '' || params[key] === undefined || params[key] === null) {
      delete params[key];
    }
  });

  const res = (await getUserList(params)) as unknown as { list: User[]; total: number };
  console.log(res);
  tableData.value = res.list;
  total.value = res.total as unknown as number;
};

/**
 * 复制邀请链接
 */
const copyInviteLink = (row: any) => {
  console.log('复制邀请链接', row);
  const inviteLink = `http://live.jiajs.cn/invite?code=${row.id}`;

  try {
    // 创建临时文本区域
    const textArea = document.createElement('textarea');
    textArea.value = inviteLink;
    // 确保文本区域不可见
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    // 选择文本并复制
    textArea.select();
    document.execCommand('copy');
    // 清理DOM
    document.body.removeChild(textArea);

    ElMessage.success('复制邀请链接成功');
  } catch (error) {
    console.error('复制失败:', error);
    ElMessage.error('复制失败，请手动复制');
  }
};

onMounted(() => {
  getUserListData();
  // 加载部门、岗位和角色下拉选项数据
  getDepartmentList();
  getPositionList();
  getRoleList();
});

/**
 * 处理每页显示数量变化
 * @param {number} size - 新的页面大小
 */
const handleSizeChange = (size: number) => {
  pageSize.value = size;
  // 切换每页条数后，重新加载数据
  getUserListData();
};

/**
 * 处理页码变化
 * @param {number} page - 新的页码
 */
const handleCurrentChange = (page: number) => {
  currentPage.value = page;
  getUserListData();
};

// 对话框相关
const dialogVisible = ref(false);
const dialogType = ref<'add' | 'edit'>('add');
const form = ref({
  id: null,
  account: '',
  password: '',
  tell: '',
  department_id: undefined as number | undefined,
  post_id: undefined as number | undefined,
  role_id: undefined as string | undefined,
  status: true,
});
const formRef = ref<FormInstance>();

const formRules = reactive<FormRules>({
  account: [{ required: true, message: '请输入账号', trigger: 'blur' }],
  password: [{ required: false, message: '请输入密码', trigger: 'blur' }],
  tell: [
    { required: false, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式', trigger: 'blur' },
  ],
  department_id: [{ required: false, message: '请选择部门', trigger: 'change' }],
  post_id: [{ required: false, message: '请选择岗位', trigger: 'change' }],
  role_id: [{ required: true, message: '请选择角色', trigger: 'change' }],
});

/**
 * 处理表单提交
 * 根据对话框类型执行不同操作
 * 使用RESTful API进行数据交互，code=0表示成功
 */
const handleSubmit = async () => {
  if (!formRef.value) return;

  await formRef.value.validate(async valid => {
    if (valid) {
      try {
        console.log('提交', form.value);
        if (dialogType.value === 'add') {
          // 新增密码必填
          if (!form.value.password) {
            ElMessage.error('密码不能为空');
            return;
          }
          await addUser({
            account: form.value.account,
            password: encryptPassword(form.value.password),
            tell: form.value.tell,
            department_id: form.value.department_id,
            post_id: form.value.post_id,
            role_id: form.value.role_id,
          });
          ElMessage.success('新增用户成功');
        } else {
          // 修改密码非必填
          let password = '';
          if (form.value.password) {
            password = encryptPassword(form.value.password);
          }
          await updateUser(form.value.id, {
            account: form.value.account,
            password: password,
            tell: form.value.tell,
            department_id: form.value.department_id,
            post_id: form.value.post_id,
            role_id: form.value.role_id,
            status: form.value.status ? 1 : -1,
          });
          ElMessage.success('编辑用户成功');
        }
        dialogVisible.value = false;
        getUserListData();
        // 初始化表单
        form.value = {
          id: null,
          account: '',
          password: '',
          tell: '',
          department_id: undefined,
          post_id: undefined,
          role_id: undefined,
          status: true,
        };
      } catch (err) {
        console.log(err);

        if (dialogType.value === 'add') {
          ElMessage.error('新增用户失败');
        } else {
          ElMessage.error('编辑用户失败');
        }
      }
    }
  });
};

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1; // 重置到第一页
  getUserListData();
};

// 重置搜索
const resetSearch = () => {
  searchForm.value = {
    account: '',
    tell: '',
    department_id: undefined,
    post_id: undefined,
    role_id: undefined,
  };
  currentPage.value = 1; // 重置到第一页
  getUserListData();
};

// 处理左侧部门筛选
const handleFilterByDepartment = (departmentId: number) => {
  searchForm.value.department_id = departmentId;
  handleSearch();
};
</script>

<style scoped lang="scss">
.user-container {
  flex: 1;
  height: 100%;
  display: flex;
  height: 100%;
  overflow: hidden;
}

.role-container {
  width: 260px;
  height: 100%;
  overflow: auto;
}

.main-container {
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.search-card {
  margin-bottom: 8px;
  width: 100%;
}

.table-card {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: calc(100% - 80px); /* 减去搜索卡片和其margin的高度 */
}

/* 确保表格内容区域正确滚动 */
.el-table {
  flex: 1;
  overflow: hidden;
}

.el-table__body-wrapper {
  overflow-y: auto;
}
</style>
