module.exports = {
  // 一行最多字符数
  printWidth: 100,
  // 使用单引号
  singleQuote: true,
  // 在语句末尾使用分号
  semi: true,
  // 缩进使用2个空格
  tabWidth: 2,
  // 对象属性使用引号的方式
  quoteProps: 'as-needed',
  // 在对象或数组最后一个元素后面是否加逗号
  trailingComma: 'es5',
  // 大括号内的首尾需要空格
  bracketSpacing: true,
  // 箭头函数，只有一个参数的时候，也需要括号
  arrowParens: 'avoid',
  // HTML空白敏感度
  htmlWhitespaceSensitivity: 'ignore',
  // 不缩进Vue文件中的script和style标签
  vueIndentScriptAndStyle: false,
  // 换行符使用auto
  endOfLine: 'auto',
  // Vue模板中的每个属性占一行，解决max-attributes-per-line警告
  singleAttributePerLine: true
} 