<template>
  <div class="payment-container">
    <el-card class="payment-card">
      <!-- 搜索部分 -->
      <div class="filter-container">
        <el-form
          :inline="true"
          :model="searchForm"
          class="demo-form-inline"
        >
          <el-form-item label="支付状态">
            <div style="width: 180px">
              <el-select
                v-model="searchForm.status"
                placeholder="全部"
                clearable
              >
                <el-option
                  v-for="status in statusOptions"
                  :key="status.value"
                  :label="status.label"
                  :value="status.value"
                />
              </el-select>
            </div>
          </el-form-item>
          <el-form-item label="日期范围">
            <div style="width: 380px">
              <el-date-picker
                v-model="dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="YYYY-MM-DD"
                @change="handleDateChange"
              />
            </div>
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              @click="handleSearch"
            >
              搜索
            </el-button>
            <el-button @click="resetSearchForm">重置</el-button>
            <el-button
              type="success"
              @click="handleExport"
              :loading="exportLoading"
            >
              导出
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 数据表格 -->
      <el-table
        v-loading="tableLoading"
        :data="tableData"
        border
        stripe
        style="width: 100%"
      >
        <el-table-column
          prop="id"
          label="ID"
          width="80"
          align="center"
        />
        <el-table-column
          prop="OutTradeNo"
          label="订单号"
          width="200"
        />
        <el-table-column
          prop="PackageName"
          label="套餐名称"
          width="200"
        />
        <el-table-column
          prop="PackageDay"
          label="套餐天数"
          width="80"
        />
        <el-table-column
          prop="Total"
          label="订单金额"
          width="120"
        >
          <template #default="scope">
            <span>¥ {{ (scope.row.Total / 100).toFixed(2) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="PayTotal"
          label="实际支付金额"
          width="120"
        >
          <template #default="scope">
            <span>¥ {{ (scope.row.PayTotal / 100).toFixed(2) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="Status"
          label="支付状态"
          width="100"
        >
          <template #default="scope">
            <el-tag
              :type="
                scope.row.Status === 1 ? 'warning' : scope.row.Status === 2 ? 'success' : 'danger'
              "
            >
              {{ formatStatus(scope.row.Status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="Uid"
          label="用户ID"
          width="100"
        />
        <el-table-column
          prop="share_id"
          label="分享ID"
        />
        <el-table-column
          prop="PaymentDate"
          label="支付时间"
          width="180"
        >
          <template #default="scope">
            <span>{{ formatDate(scope.row.PaymentDate) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="PaymentMethod"
          label="支付方式"
          width="100"
        />
        <el-table-column
          prop="CreatedAt"
          label="创建时间"
          width="180"
        >
          <template #default="scope">
            <span>{{ formatDate(scope.row.CreatedAt) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="UpdatedAt"
          label="更新时间"
          width="180"
        >
          <template #default="scope">
            <span>{{ formatDate(scope.row.UpdatedAt) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          fixed="right"
          width="200"
        >
          <template #default="scope">
            <el-button
              size="small"
              type="danger"
              @click="handleDelete(scope.row.id)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getPaymentList, PaymentDetail, exportPaymentList } from '@/api/payment';
import { formatDate } from '@/utils/tools';

// 支付状态选项
const statusOptions = [
  { value: 1, label: '未支付' },
  { value: 2, label: '已支付' },
  { value: 3, label: '退款' },
];

const formatStatus = status => {
  switch (status) {
    case 1:
      return '未支付';
    case 2:
      return '已支付';
    case 3:
      return '退款';
    default:
      return '';
  }
};

// 日期范围
const dateRange = ref(null);

// 处理日期变化
const handleDateChange = val => {
  if (val) {
    searchForm.start_time = val[0];
    searchForm.end_time = val[1];
  } else {
    searchForm.start_time = null;
    searchForm.end_time = null;
  }
};

// 搜索表单
const searchForm = reactive({
  status: null,
  start_time: null,
  end_time: null,
});

// 表格数据
const tableData = ref<PaymentDetail[]>([]);
const tableLoading = ref(false);
const exportLoading = ref(false);

// 分页数据
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0,
});

// 获取支付明细列表
const getPaymentListData = async () => {
  tableLoading.value = true;

  try {
    const params = {
      page: pagination.page,
      page_size: pagination.pageSize,
      ...searchForm,
    };

    const res = (await getPaymentList(params)) as unknown as { list: []; total: number };

    tableData.value = res.list;
    pagination.total = res.total;
  } catch (error) {
    console.error('获取支付明细列表失败', error);
    ElMessage.error('获取支付明细列表失败');
  } finally {
    tableLoading.value = false;
  }
};

// 导出支付明细
const handleExport = async () => {
  exportLoading.value = true;
  try {
    // 构建导出参数，不包含分页参数
    const exportParams = { ...searchForm };
    await exportPaymentList(exportParams);
    ElMessage.success('导出成功');
  } catch (error) {
    console.error('导出支付明细失败', error);
    // 错误消息已在downloadFile方法中处理
  } finally {
    exportLoading.value = false;
  }
};

// 搜索操作
const handleSearch = () => {
  pagination.page = 1;
  getPaymentListData();
};

// 重置搜索表单
const resetSearchForm = () => {
  searchForm.status = null;
  searchForm.start_time = null;
  searchForm.end_time = null;
  dateRange.value = null;
  handleSearch();
};

// 分页大小改变事件
const handleSizeChange = (newSize: number) => {
  pagination.pageSize = newSize;
  getPaymentListData();
};

// 页码改变事件
const handleCurrentChange = (newPage: number) => {
  pagination.page = newPage;
  getPaymentListData();
};

// 删除支付记录
const handleDelete = (id: number) => {
  ElMessageBox.confirm('确定要删除该支付记录吗？', '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(() => {
      // 这里可以调用删除API
      // 实际使用时需替换为真实的删除API调用
      console.log(id);
      ElMessage.success('删除成功');
      getPaymentListData();
    })
    .catch(() => {
      ElMessage.info('已取消删除');
    });
};

// 组件挂载时获取数据
onMounted(() => {
  getPaymentListData();
});
</script>

<style scoped>
.payment-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  font-size: 24px;
  margin-bottom: 8px;
}

.page-header p {
  font-size: 14px;
  color: #666;
  margin: 0;
}

.payment-card {
  margin-bottom: 20px;
}

.filter-container {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
