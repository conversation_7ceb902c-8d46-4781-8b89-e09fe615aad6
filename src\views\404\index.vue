<template>
  <div class="not-found-container">
    <div class="not-found-content">
      <h1 class="not-found-title">404</h1>
      <div class="not-found-desc">抱歉，您访问的页面不存在</div>
      <div class="not-found-actions">
        <el-button
          type="primary"
          @click="goHome"
        >
          返回首页
        </el-button>
        <el-button @click="goBack">返回上一页</el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router';

const router = useRouter();

const goHome = () => {
  router.push('/');
};

const goBack = () => {
  router.go(-1);
};
</script>

<style scoped>
.not-found-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f0f2f5;
}

.not-found-content {
  text-align: center;
}

.not-found-title {
  font-size: 120px;
  font-weight: bold;
  color: #409eff;
  margin: 0;
  text-shadow: 4px 4px 10px rgba(0, 0, 0, 0.1);
}

.not-found-desc {
  font-size: 20px;
  color: #666;
  margin: 20px 0 30px;
}

.not-found-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
}
</style>
