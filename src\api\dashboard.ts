import request from '@/utils/request';
import { StatsData, ChartData, TodoItem, NoticeItem, UserRankingItem } from './types';

// 获取首页统计数据
export function getStats(): Promise<StatsData> {
  return request<StatsData>({
    url: '/api/dashboard/stats',
    method: 'get',
  });
}

// 获取图表数据
export function getChartData(type = 'week'): Promise<ChartData> {
  return request<ChartData>({
    url: '/api/dashboard/chart',
    method: 'get',
    params: { type },
  });
}

// 获取待办事项
export function getTodos(): Promise<TodoItem[]> {
  return request<TodoItem[]>({
    url: '/api/dashboard/todos',
    method: 'get',
  });
}

// 获取系统通知
export function getNotices(): Promise<NoticeItem[]> {
  return request<NoticeItem[]>({
    url: '/api/dashboard/notices',
    method: 'get',
  });
}

// 获取用户排行榜
export function getUserRanking(params: {
  start_time?: string;
  end_time?: string;
  limit?: number;
}): Promise<UserRankingItem[]> {
  return request<UserRankingItem[]>({
    url: '/api/v1/order/rank',
    method: 'post',
    data: params,
  });
}
