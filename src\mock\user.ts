import Mock from 'mockjs';

const Random = Mock.Random;

// 用户类型定义
interface User {
  id: string;
  username: string;
  password: string;
  avatar: string;
  name: string;
  email: string;
  role: string;
  permissions: string[];
}

// 模拟用户数据
const users: Record<string, User> = {
  admin: {
    id: '1',
    username: 'admin',
    password: '123456',
    avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
    name: '管理员',
    email: '<EMAIL>',
    role: 'admin',
    permissions: ['admin', 'edit', 'view'],
  },
  editor: {
    id: '2',
    username: 'editor',
    password: '123456',
    avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
    name: '编辑者',
    email: '<EMAIL>',
    role: 'editor',
    permissions: ['edit', 'view'],
  },
  user: {
    id: '3',
    username: 'user',
    password: '123456',
    avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
    name: '普通用户',
    email: '<EMAIL>',
    role: 'user',
    permissions: ['view'],
  },
};


// 获取用户信息接口
Mock.mock(/\/user\/info/, 'get', () => {
  // 这里可以解析请求头中的 token，并返回对应的用户信息
  // 为简化示例，这里直接返回管理员信息

  return {
    code: 200,
    message: '获取成功',
    data: users.admin,
  };
});

// 退出登录接口
Mock.mock(/\/user\/logout/, 'post', () => {
  return {
    code: 200,
    message: '退出登录成功',
    data: null,
  };
});

export function setupUserMock() {
  // 这个函数在mock/index.js中被调用，用于初始化所有模拟数据
  // 如果需要添加更多的用户相关接口，可以在这里继续添加
  console.log('User mock data initialized');
}
