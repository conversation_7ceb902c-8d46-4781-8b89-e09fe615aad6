<template>
  <div class="navbar">
    <!-- 折叠按钮 -->
    <div
      class="hamburger-container"
      @click="toggleSidebar"
    >
      <el-icon
        class="hamburger-icon"
        :class="{ 'is-active': isCollapsed }"
      >
        <FoldIcon />
      </el-icon>
    </div>

    <!-- 面包屑导航 -->
    <div class="breadcrumb-container">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/home' }">
          <el-icon><component :is="icons.HomeFilled" /></el-icon>
          <span>首页</span>
        </el-breadcrumb-item>
        <template
          v-for="(item, index) in breadcrumbList"
          :key="item.path"
        >
          <el-breadcrumb-item
            :to="index < breadcrumbList.length - 1 ? { path: item.path } : ''"
            :class="{ 'is-last': index === breadcrumbList.length - 1 }"
          >
            <el-icon v-if="item.icon && icons[item.icon]">
              <component :is="icons[item.icon]" />
            </el-icon>
            <span>{{ item.title }}</span>
          </el-breadcrumb-item>
        </template>
      </el-breadcrumb>
    </div>

    <!-- 右侧菜单 -->
    <div class="right-menu">
      <!-- 全屏按钮 -->
      <div
        class="right-menu-item hover-effect"
        @click="toggleFullscreen"
      >
        <el-tooltip
          effect="dark"
          :content="isFullscreen ? '退出全屏' : '全屏'"
          placement="bottom"
        >
          <el-icon class="right-menu-icon">
            <component :is="fullscreenIcon" />
          </el-icon>
        </el-tooltip>
      </div>

      <!-- 用户下拉菜单 -->
      <el-dropdown
        class="avatar-container"
        trigger="click"
      >
        <div class="avatar-wrapper">
          <img
            :src="avatarUrl"
            class="user-avatar"
          />
          <span class="user-name">{{ username }}</span>
          <el-icon><CaretBottomIcon /></el-icon>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item @click="toUserInfo">
              <el-icon><UserIcon /></el-icon>
              <span>个人中心</span>
            </el-dropdown-item>
            <el-dropdown-item
              divided
              @click="handleLogout"
            >
              <el-icon><SwitchButtonIcon /></el-icon>
              <span>退出登录</span>
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch, markRaw } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { ElMessageBox, ElMessage } from 'element-plus';
import {
  FullScreen,
  Aim,
  CaretBottom,
  User,
  SwitchButton,
  Fold,
  HomeFilled,
  Menu as MenuIcon,
  Setting,
  Document,
  Avatar,
} from '@element-plus/icons-vue';
import { useUserStore } from '@/store/user';
import { logout } from '@/api/system/user';

// 使用 markRaw 标记图标组件
const FullScreenIcon = markRaw(FullScreen);
const AimIcon = markRaw(Aim);
const CaretBottomIcon = markRaw(CaretBottom);
const UserIcon = markRaw(User);
const SwitchButtonIcon = markRaw(SwitchButton);
const FoldIcon = markRaw(Fold);

// 所有图标集合
const icons: Record<string, any> = {
  FullScreen: FullScreenIcon,
  Aim: AimIcon,
  HomeFilled: markRaw(HomeFilled),
  Setting: markRaw(Setting),
  User: UserIcon,
  Menu: markRaw(MenuIcon),
  Document: markRaw(Document),
  Avatar: markRaw(Avatar),
};

// 接收父组件传递的属性
const props = defineProps({
  toggleSidebar: {
    type: Function,
    required: true,
  },
  isCollapsed: {
    type: Boolean,
    default: false,
  },
});

const router = useRouter();
const route = useRoute();
const userStore = useUserStore();

// 确保用户信息已加载
const ensureUserInfo = () => {
  if (!userStore.userInfo || Object.keys(userStore.userInfo).length === 0) {
    console.log('重新初始化用户信息');
    userStore.initUserInfo();
  }
};

// 在组件挂载和路由变化时确保用户信息已加载
watch(
  () => route.path,
  () => {
    ensureUserInfo();
  },
  { immediate: true }
);

// 全屏状态
const isFullscreen = ref(false);

// 用户信息
const username = computed(() => {
  console.log('Navbar中的用户信息:', userStore.userInfo);
  return userStore.userInfo?.name || userStore.userInfo?.username || '未登录';
});
const avatarUrl = computed(
  () =>
    userStore.userInfo?.avatar ||
    'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'
);

// 面包屑导航数据
interface BreadcrumbItem {
  title: string;
  path: string;
  icon?: string;
}

const breadcrumbList = ref<BreadcrumbItem[]>([]);

// 生成面包屑导航数据
const generateBreadcrumb = () => {
  breadcrumbList.value = [];

  const matched = route.matched.filter(item => item.meta && item.meta.title);

  if (matched.length > 0) {
    // 如果当前路由是根路由的子路由，不显示根路由
    const routeList = matched[0].path === '/' ? matched.slice(1) : matched;

    for (let i = 0; i < routeList.length; i++) {
      const item = routeList[i];
      const meta = item.meta || {};

      breadcrumbList.value.push({
        title: meta.title as string,
        path: item.path,
        icon: meta.icon as string,
      });
    }
  }
};

// 监听路由变化，生成面包屑数据
watch(
  () => route.path,
  () => {
    generateBreadcrumb();
  },
  { immediate: true }
);

// 调用父组件的切换侧边栏方法
const toggleSidebar = () => {
  props.toggleSidebar();
};

// 切换全屏
const toggleFullscreen = () => {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen();
    isFullscreen.value = true;
  } else {
    if (document.exitFullscreen) {
      document.exitFullscreen();
      isFullscreen.value = false;
    }
  }
};

// 跳转到个人中心
const toUserInfo = () => {
  router.push('/user/profile');
};

// 退出登录
const handleLogout = () => {
  ElMessageBox.confirm('确定要退出登录吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      try {
        await logout(router);
        ElMessage.success('退出登录成功');
      } catch (error) {
        ElMessage.error('退出登录失败' + error);
      }
    })
    .catch(() => {
      // 用户取消退出操作
      console.log('用户取消退出操作');
    });
};

// 修改全屏图标组件
const fullscreenIcon = computed(() => (isFullscreen.value ? AimIcon : FullScreenIcon));
</script>

<style scoped>
.navbar {
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 15px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  background-color: #fff;
}

.hamburger-container {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 0 15px;
  cursor: pointer;
}

.hamburger-icon {
  font-size: 20px;
  color: #666;
  transition: transform 0.3s;
}

.hamburger-icon.is-active {
  transform: rotate(180deg);
}

.breadcrumb-container {
  display: flex;
  align-items: center;
  flex: 1;
  margin-left: 10px;
}

:deep(.el-breadcrumb__inner) {
  display: inline-flex !important;
  align-items: center;
}

:deep(.el-breadcrumb__inner) .el-icon {
  margin-right: 5px;
  font-size: 14px;
}

:deep(.el-breadcrumb__item) {
  display: inline-flex;
  align-items: center;
}

:deep(.el-breadcrumb__item.is-last .el-breadcrumb__inner) {
  font-weight: bold;
  color: #409eff;
}

:deep(.el-breadcrumb__separator) {
  margin: 0 8px;
  color: #c0c4cc;
}

:deep(.el-breadcrumb__item:last-child .el-breadcrumb__inner) {
  cursor: default;
}

.right-menu {
  display: flex;
  align-items: center;
}

.right-menu-item {
  padding: 0 8px;
  cursor: pointer;
  font-size: 20px;
  color: #666;
  transition: all 0.3s;
}

.right-menu-item:hover {
  color: #409eff;
}

.avatar-container {
  margin-left: 10px;
}

.avatar-wrapper {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 0 8px;
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  margin-right: 8px;
}

.user-name {
  font-size: 14px;
  color: #333;
  margin-right: 4px;
}

.hover-effect {
  cursor: pointer;
  transition: all 0.3s;
}

.hover-effect:hover {
  opacity: 0.8;
}
</style>
