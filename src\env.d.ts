/// <reference types="vite/client" />

interface ImportMetaEnv {
  readonly VITE_APP_TITLE: string;
  readonly VITE_APP_BASE_API: string;
  readonly VITE_APP_API_PREFIX: string;
  readonly VITE_APP_USE_MOCK: string;
  readonly VITE_APP_PORT: string;
  readonly VITE_APP_DEBUG: string;
  readonly VITE_APP_UPLOAD_API: string;
  readonly VITE_APP_CDN_URL: string;
  readonly VITE_APP_ERROR_LOG_URL: string;
  readonly VITE_APP_MODE: string;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}

// 声明vue文件模块，解决导入vue文件的类型问题
declare module '*.vue' {
  import { DefineComponent } from 'vue';
  const component: DefineComponent<object, object, any>;
  export default component;
}
