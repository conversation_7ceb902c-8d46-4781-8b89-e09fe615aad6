import Mock from 'mockjs';
import { PaymentDetail } from '../api/payment';

const Random = Mock.Random;

// 生成随机支付方式
const paymentMethods = ['微信支付', '支付宝', '银行卡', '余额支付'];
const paymentStatus = ['待支付', '支付成功', '支付失败', '已退款', '退款中'];

// 生成模拟支付明细数据
const generatePaymentList = (count: number = 100) => {
  const result: PaymentDetail[] = [];

  for (let i = 0; i < count; i++) {
    const createTime = Random.datetime('yyyy-MM-dd HH:mm:ss');
    const status = Random.pick(paymentStatus);

    let payTime = null;
    if (status !== '待支付') {
      // 如果不是待支付状态，则生成支付时间
      payTime = Random.datetime('yyyy-MM-dd HH:mm:ss');
    }

    result.push({
      id: i + 1,
      orderNo: `ORD${Random.date('yyyyMMdd')}${Random.string('number', 8)}`,
      paymentNo: `PAY${Random.date('yyyyMMdd')}${Random.string('number', 8)}`,
      amount: Number(Random.float(10, 10000, 2, 2).toFixed(2)),
      paymentMethod: Random.pick(paymentMethods),
      status,
      userName: Random.cname(),
      userPhone: `1${Random.string('number', 10)}`,
      createTime,
      payTime,
    });
  }

  return result;
};

// 模拟的支付明细数据
const paymentListData = generatePaymentList();

// 获取支付明细列表
Mock.mock(/\/api\/payment\/list/, 'get', (options: any) => {
  const url = new URL(`http://example.com${options.url}`);
  const page = parseInt(url.searchParams.get('page') || '1');
  const pageSize = parseInt(url.searchParams.get('pageSize') || '10');
  const keyword = url.searchParams.get('keyword') || '';
  const status = url.searchParams.get('status') || '';
  const paymentMethod = url.searchParams.get('paymentMethod') || '';

  let filteredList = [...paymentListData];

  // 根据关键字过滤
  if (keyword) {
    filteredList = filteredList.filter(
      item =>
        item.orderNo.includes(keyword) ||
        item.paymentNo.includes(keyword) ||
        item.userName.includes(keyword) ||
        (item.userPhone && item.userPhone.includes(keyword))
    );
  }

  // 根据状态过滤
  if (status) {
    filteredList = filteredList.filter(item => item.status === status);
  }

  // 根据支付方式过滤
  if (paymentMethod) {
    filteredList = filteredList.filter(item => item.paymentMethod === paymentMethod);
  }

  // 计算总数
  const total = filteredList.length;

  // 分页
  const start = (page - 1) * pageSize;
  const end = page * pageSize;
  const pagedList = filteredList.slice(start, end);

  return {
    code: 200,
    message: '获取成功',
    data: {
      list: pagedList,
      total,
    },
  };
});

// 获取支付明细详情
Mock.mock(/\/api\/payment\/detail\/\d+/, 'get', (options: any) => {
  const url = options.url;
  const id = parseInt(url.match(/\/api\/payment\/detail\/(\d+)/)[1]);

  const payment = paymentListData.find(item => item.id === id);

  if (payment) {
    return {
      code: 200,
      message: '获取成功',
      data: payment,
    };
  } else {
    return {
      code: 404,
      message: '支付记录不存在',
      data: null,
    };
  }
});

// 更新支付状态
Mock.mock(/\/api\/payment\/status/, 'put', (options: any) => {
  const { id, status } = JSON.parse(options.body);

  const payment = paymentListData.find(item => item.id === id);

  if (payment) {
    payment.status = status;
    if (status === '支付成功' && !payment.payTime) {
      payment.payTime = Random.datetime('yyyy-MM-dd HH:mm:ss');
    }

    return {
      code: 200,
      message: '状态更新成功',
      data: null,
    };
  } else {
    return {
      code: 404,
      message: '支付记录不存在',
      data: null,
    };
  }
});

// 退款操作
Mock.mock(/\/api\/payment\/refund/, 'post', (options: any) => {
  const { id, reason } = JSON.parse(options.body);

  const payment = paymentListData.find(item => item.id === id);

  if (payment) {
    if (payment.status === '支付成功') {
      payment.status = '退款中';

      return {
        code: 200,
        message: '退款申请已提交',
        data: null,
      };
    } else {
      return {
        code: 400,
        message: '只有支付成功的订单才能申请退款',
        data: null,
      };
    }
  } else {
    return {
      code: 404,
      message: '支付记录不存在',
      data: null,
    };
  }
});
