/**
 * 用户管理API模块
 * 提供用户相关的增删改查接口
 * 遵循RESTful设计规范
 * @module api/system/user
 */
import request from '@/utils/request';
import { useUserStore } from '@/store/user';
import { Router } from 'vue-router';

/**
 * 用户数据接口
 * @interface User
 */
export interface User {
  /** 用户ID */
  id?: number;
  /** 用户账号 */
  account?: string;
  /** 用户密码（加密后） */
  password?: string;
  /** 手机号 */
  tell?: string;
  /** 部门ID */
  department_id?: number;
  /** 岗位ID */
  post_id?: number;
  /** 角色ID */
  role_id?: string;
  /** 创建时间 */
  created_at?: string;
  /** 最后登录IP */
  last_ip?: string;
  /** 最后登录时间 */
  last_time?: number;
  /** 用户状态 -1: 禁用 1: 正常 */
  status?: number;
  /** 更新时间 */
  updated_at?: string;
}

/**
 * 登录参数接口
 * @interface LoginParams
 */
export interface LoginParams {
  /** 用户账号 */
  account: string;
  /** 用户密码 */
  password: string;
}

/**
 * 新增用户参数接口
 * @interface AddUserParams
 */
export interface AddUserParams {
  /** 用户密码 */
  password?: string;
  /** 用户账号 */
  account?: string;
  /** 手机号 */
  tell?: string;
  /** 部门ID */
  department_id?: number;
  /** 岗位ID */
  post_id?: number;
  /** 角色ID */
  role_id?: string;
}

/**
 * 新增用户
 * RESTful: POST /api/v1/admin/
 * @param {AddUserParams} data - 用户数据
 * @returns {Promise<null>} 操作结果，code=0表示成功
 */
export function addUser(data: AddUserParams) {
  return request<null>({
    url: '/api/v1/admin/',
    method: 'post',
    data,
  });
}
// 用户列表
export function getUserList(params: any) {
  return request<{ list: User[]; total: number }>({
    url: '/api/v1/admin/',
    method: 'get',
    params,
  });
}
// 删除用户
export function deleteUser(id: number) {
  return request<null>({
    url: `/api/v1/admin/${id}`,
    method: 'delete',
  });
}
// 修改用户
export function updateUser(id: number | null, data: any) {
  return request<null>({
    url: `/api/v1/admin/${id}`,
    method: 'put',
    data,
  });
}
/**
 * 登录接口
 * RESTful: POST /api/admin/login
 * @param {LoginParams} data - 登录参数
 * @returns {Promise<{ token: string; userInfo: User }>} 登录结果，包含token和用户信息，code=0表示成功
 */
export function login(data: LoginParams) {
  return request<{ token: string }>({
    url: '/api/admin/login',
    method: 'post',
    data,
  });
}

/**
 * 退出登录接口
 * RESTful: POST /api/user/logout
 * @param {Router} router - Vue Router实例
 * @returns {Promise<null>} 操作结果，code=0表示成功
 */
export function logout(router: Router) {
  // 在函数内部获取store
  const userStore = useUserStore();

  // 清除用户信息
  userStore.clearUserInfo();

  // 使用 router.replace 进行跳转
  try {
    console.log(router);
    router.replace('/login');
  } catch (error) {
    console.error('路由跳转失败:', error);
  }
  return;
}

/**
 * 获取用户信息接口
 * RESTful: GET /api/user/info
 * @returns {Promise<User>} 用户信息，code=0表示成功
 */
export function getUserInfo() {
  return request<User>({
    url: '/api/user/info',
    method: 'get',
  });
}

/**
 * 更新用户状态
 * RESTful: PUT /api/user/status
 * @param {number} id - 用户ID
 * @param {number} status - 用户状态
 * @returns {Promise<null>} 操作结果，code=0表示成功
 */
export function updateUserStatus(id: number, status: number) {
  return request<null>({
    url: '/api/user/status',
    method: 'put',
    data: { id, status },
  });
}
