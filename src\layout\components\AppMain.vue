<template>
  <section class="app-main">
    <el-card class="app-main-card">
      <router-view v-slot="{ Component }">
        <transition
          name="fade-transform"
          mode="out-in"
        >
          <keep-alive>
            <component :is="Component" />
          </keep-alive>
        </transition>
      </router-view>
    </el-card>
  </section>
</template>

<style scoped>
.app-main {
  height: calc(100vh - 84px - 50px);
  padding: 15px;
  overflow-y: auto;
  background-color: #f5f7fa;
  flex: 1;
}

.app-main-card {
  height: 100%;
  overflow-y: auto;
}

/* 当显示底部时调整高度 */
:deep(.has-footer) .app-main {
  height: calc(100vh - 84px - 50px - 60px);
}

/* 页面切换动画 */
.fade-transform-enter-active,
.fade-transform-leave-active {
  transition: all 0.3s;
}

.fade-transform-enter-from {
  opacity: 0;
  transform: translateX(-20px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(20px);
}
</style>
