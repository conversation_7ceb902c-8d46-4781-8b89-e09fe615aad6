<template>
  <div class="menu-container">
    <div class="menu-header">
      <el-card>
        <div class="header-operations">
          <el-button
            type="primary"
            @click="handleAdd"
          >
            新增菜单
          </el-button>
        </div>
      </el-card>
    </div>

    <el-card class="menu-content">
      <el-table
        v-loading="loading"
        :data="menuList"
        border
        row-key="id"
        default-expand-all
        :tree-props="{ children: 'children' }"
      >
        <el-table-column
          prop="name"
          label="菜单名称"
          min-width="200"
        />
        <el-table-column
          prop="icon"
          label="图标"
          width="100"
        >
          <template #default="{ row }">
            <el-icon v-if="row.icon">
              <component :is="row.icon" />
            </el-icon>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <!-- <el-table-column
          prop="path"
          label="路由路径"
          min-width="200"
        /> -->
        <el-table-column
          prop="group_path"
          label="组件路径"
          min-width="200"
        />
        <el-table-column
          prop="sort"
          label="排序"
          width="100"
        />
        <el-table-column
          prop="is_show"
          label="是否显示"
          width="100"
        >
          <template #default="{ row }">
            <el-tag
              v-if="row.is_show === -1"
              type="warning"
            >
              隐藏
            </el-tag>
            <el-tag
              v-else
              type="success"
            >
              显示
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          width="220"
          fixed="right"
        >
          <template #default="{ row }">
            <el-button
              type="primary"
              link
              @click="handleEdit(row)"
            >
              编辑
            </el-button>
            <el-button
              type="primary"
              link
              @click="handleAddChild(row)"
            >
              添加子菜单
            </el-button>
            <el-button
              type="danger"
              link
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 新增/编辑菜单对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'add' ? '新增菜单' : '编辑菜单'"
      width="600px"
    >
      <el-form
        ref="menuFormRef"
        :model="menuForm"
        :rules="menuRules"
        label-width="100px"
      >
        <el-form-item
          label="上级菜单"
          prop="parent_id"
        >
          <el-tree-select
            v-model="menuForm.parent_id"
            :data="menuTreeOptions"
            node-key="id"
            :props="{ label: 'name', children: 'children' }"
            check-strictly
            default-expand-all
            placeholder="请选择上级菜单"
            clearable
          />
        </el-form-item>
        <el-form-item
          label="菜单名称"
          prop="name"
        >
          <el-input
            v-model="menuForm.name"
            placeholder="请输入菜单名称"
          />
        </el-form-item>
        <el-form-item
          label="菜单图标"
          prop="icon"
        >
          <el-select
            v-model="menuForm.icon"
            placeholder="请选择图标"
            clearable
          >
            <el-option
              v-for="icon in iconList"
              :key="icon"
              :label="icon"
              :value="icon"
            >
              <div class="icon-option">
                <el-icon>
                  <component :is="icon" />
                </el-icon>
                <span>{{ icon }}</span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="路由路径"
          prop="path"
        >
          <el-input
            v-model="menuForm.path"
            placeholder="请输入路由路径"
          />
        </el-form-item>
        <el-form-item
          label="组件路径"
          prop="group_path"
        >
          <el-input
            v-model="menuForm.group_path"
            placeholder="请输入组件路径"
          />
        </el-form-item>
        <el-form-item
          label="排序"
          prop="sort"
        >
          <el-input-number
            v-model="menuForm.sort"
            :min="0"
          />
        </el-form-item>
        <el-form-item
          label="是否显示"
          prop="is_show"
        >
          <el-switch
            v-model="isShowSwitch"
            :active-value="1"
            :inactive-value="-1"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="submitForm"
        >
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import * as icons from '@element-plus/icons-vue';
import type { FormInstance, FormRules } from 'element-plus';
import { getMenuList, addMenu, updateMenu, deleteMenu, MenuItem } from '@/api/system/menu';

// 加载状态
const loading = ref(false);

// 图标列表
const iconList = computed(() => {
  return Object.keys(icons);
});

// 菜单列表
const menuList = ref<MenuItem[]>([]);

type MenuTreeOptions = {
  id: number;
  name: string;
  children: MenuItem[];
};
// 菜单树选项
const menuTreeOptions = ref<MenuTreeOptions[]>([]);

// 获取菜单数据
const fetchMenuData = async () => {
  loading.value = true;
  try {
    console.log('正在获取菜单数据...');
    const res = await getMenuList();
    console.log('获取到的菜单数据:', res);
    // 根据实际返回的数据结构处理,排除掉id为0的菜单
    menuList.value = (res as unknown as MenuItem[]).filter(item => item.id !== 0);
    menuTreeOptions.value = (res as unknown as MenuItem[]).map(item => ({
      id: item.id,
      name: item.name,
      children: item.children ? item.children : [],
    }));
    console.log('处理后的菜单数据:', menuTreeOptions.value);
  } catch (error) {
    console.error('获取菜单列表失败:', error);
    ElMessage.error('获取菜单列表失败');
  } finally {
    loading.value = false;
  }
};

// 对话框相关
const dialogVisible = ref(false);
const dialogType = ref<'add' | 'edit'>('add');
const menuFormRef = ref<FormInstance>();
const menuForm = reactive<Partial<MenuItem>>({
  id: 0,
  parent_id: 0,
  name: '',
  icon: '',
  path: '',
  group_path: '',
  sort: 0,
  is_show: 1,
});

// is_show 开关控制
const isShowSwitch = computed({
  get: () => menuForm.is_show,
  set: val => {
    menuForm.is_show = val;
  },
});

// 表单验证规则
const menuRules = reactive<FormRules>({
  parent_id: [{ required: true, message: '请选择上级菜单', trigger: 'change' }],
  name: [{ required: true, message: '请输入菜单名称', trigger: 'blur' }],
  path: [{ required: true, message: '请输入路由路径', trigger: 'blur' }],
});

// 打开新增菜单对话框
const handleAdd = () => {
  resetForm();
  dialogType.value = 'add';
  dialogVisible.value = true;
};

// 打开添加子菜单对话框
const handleAddChild = (row: MenuItem) => {
  resetForm();
  dialogType.value = 'add';
  menuForm.parent_id = row.id;
  dialogVisible.value = true;
};

// 打开编辑菜单对话框
const handleEdit = (row: MenuItem) => {
  resetForm();
  dialogType.value = 'edit';
  Object.assign(menuForm, row);
  dialogVisible.value = true;
};

// 重置表单
const resetForm = () => {
  menuFormRef.value?.resetFields();
  Object.assign(menuForm, {
    id: 0,
    parent_id: 0,
    name: '',
    icon: '',
    path: '',
    group_path: '',
    sort: 0,
    is_show: 1,
  });
};

// 提交表单
const submitForm = async () => {
  if (!menuFormRef.value) return;

  await menuFormRef.value.validate(async valid => {
    if (valid) {
      try {
        if (dialogType.value === 'add') {
          // 添加菜单
          // 排除不需要提交的字段
          const {
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            id: _id,
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            children: _children,
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            CreatedAt: _createdAt,
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            UpdatedAt: _updatedAt,
            ...addData
          } = menuForm as MenuItem;

          await addMenu(addData);
          ElMessage.success('新增菜单成功');
        } else {
          // 编辑菜单
          // 排除不需要提交的字段
          const {
            id,
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            children: _children,
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            CreatedAt: _createdAt,
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            UpdatedAt: _updatedAt,
            ...updateData
          } = menuForm;

          if (id !== undefined) {
            await updateMenu(id, updateData);
            ElMessage.success('更新菜单成功');
          }
        }

        // 重新加载菜单数据
        fetchMenuData();
        dialogVisible.value = false;
      } catch (error) {
        console.error('保存菜单失败:', error);
        ElMessage.error('操作失败，请重试');
      }
    }
  });
};

// 删除菜单
const handleDelete = (row: MenuItem) => {
  ElMessageBox.confirm('确定要删除该菜单吗？删除后将无法恢复！', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      try {
        await deleteMenu(row.id);
        ElMessage.success('删除成功');
        // 重新加载菜单数据
        fetchMenuData();
      } catch (error) {
        console.error('删除菜单失败:', error);
        ElMessage.error('删除失败，请重试');
      }
    })
    .catch(() => {
      // 取消删除
    });
};

// 初始化
onMounted(() => {
  fetchMenuData();
});
</script>

<style scoped>
.menu-container {
  padding: 20px;
}

.menu-header {
  margin-bottom: 20px;
}

.header-operations {
  display: flex;
  justify-content: flex-start;
  gap: 10px;
}

.menu-content {
  margin-bottom: 20px;
}

.icon-option {
  display: flex;
  align-items: center;
  gap: 5px;
}
</style>
