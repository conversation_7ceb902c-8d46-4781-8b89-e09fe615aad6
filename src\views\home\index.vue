<template>
  <div class="home-container">
    <!-- 欢迎信息 -->
    <div class="welcome-section">
      <h2>欢迎回来，{{ userInfo.name || '管理员' }}</h2>
      <p class="welcome-tip">今天是 {{ currentDate }}，祝您工作愉快！</p>
    </div>

    <!-- 用户排行榜 -->
    <el-card class="ranking-card">
      <template #header>
        <div class="card-header">
          <span>邀请排行榜</span>
          <el-radio-group
            v-model="rankingPeriod"
            size="small"
            @change="fetchRankingData"
          >
            <el-radio-button value="day">今日</el-radio-button>
            <el-radio-button value="week">本周</el-radio-button>
            <el-radio-button value="month">本月</el-radio-button>
            <el-radio-button value="all">全部</el-radio-button>
          </el-radio-group>
        </div>
      </template>
      <div
        v-loading="rankingLoading"
        class="ranking-list"
      >
        <el-table
          :data="rankingData"
          style="width: 100%"
          :empty-text="'暂无排名数据'"
        >
          <el-table-column
            type="index"
            label="排名"
            width="80"
            align="center"
          >
            <template #default="scope">
              <div class="rank-cell">
                <span
                  :class="[
                    'rank-number',
                    scope.$index === 0
                      ? 'first'
                      : scope.$index === 1
                        ? 'second'
                        : scope.$index === 2
                          ? 'third'
                          : '',
                  ]"
                >
                  {{ scope.$index + 1 }}
                </span>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="share_id"
            label="用户ID"
            width="120"
          />
          <el-table-column
            prop="user_name"
            label="用户名"
          />
          <el-table-column
            prop="total_amount"
            label="金额"
            width="150"
          >
            <template #default="scope">
              <span>¥ {{ (scope.row.total_amount / 100).toFixed(2) }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useUserStore } from '@/store/user';
import { getUserRanking } from '@/api/dashboard';
import { UserRankingItem } from '@/api/types';
import { ElMessage } from 'element-plus';

// 用户信息
const userStore = useUserStore();

const userInfo = computed(() => {
  console.log('首页中的用户信息:', userStore.userInfo);
  return userStore.userInfo || {};
});

// 当前日期
const currentDate = computed(() => {
  const now = new Date();
  return now.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long',
  });
});

// 排行榜数据
const rankingData = ref<UserRankingItem[]>([]);
const rankingLoading = ref(false);
const rankingPeriod = ref<'day' | 'week' | 'month' | 'all'>('all');

// 格式化日期为YYYY-MM-DD
const formatDate = (date: Date): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

// 获取日期范围
const getDateRange = (): { start_time?: string; end_time?: string } => {
  const today = new Date();
  const endDate = formatDate(today);

  switch (rankingPeriod.value) {
    case 'day':
      return { start_time: endDate, end_time: endDate };
    case 'week': {
      const startOfWeek = new Date(today);
      const dayOfWeek = today.getDay();
      const daysToSubtract = dayOfWeek === 0 ? 6 : dayOfWeek - 1;
      startOfWeek.setDate(today.getDate() - daysToSubtract);
      return { start_time: formatDate(startOfWeek), end_time: endDate };
    }
    case 'month': {
      const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
      return { start_time: formatDate(startOfMonth), end_time: endDate };
    }
    case 'all':
    default:
      return { start_time: undefined, end_time: undefined };
  }
};

// 获取排行榜数据
const fetchRankingData = async () => {
  rankingLoading.value = true;
  try {
    const dateRange = getDateRange();
    const params = {
      ...dateRange,
    };
    const data = await getUserRanking(params);
    rankingData.value = data;
  } catch (error) {
    console.error('获取排行榜数据失败', error);
    ElMessage.error('获取排行榜数据失败');
    rankingData.value = [];
  } finally {
    rankingLoading.value = false;
  }
};

// 页面加载时获取排行榜数据
onMounted(() => {
  fetchRankingData();
});
</script>

<style scoped>
.home-container {
  width: 100%;
}

.welcome-section {
  margin-bottom: 20px;
}

.welcome-section h2 {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.welcome-tip {
  color: #909399;
  margin-top: 8px;
}

.ranking-card {
  margin-bottom: 20px;
  width: 600px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.ranking-list {
  margin-top: 10px;
}

.rank-cell {
  display: flex;
  justify-content: center;
  align-items: center;
}

.rank-number {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  font-weight: bold;
}

.rank-number.first {
  background-color: #f56c6c;
  color: #fff;
}

.rank-number.second {
  background-color: #e6a23c;
  color: #fff;
}

.rank-number.third {
  background-color: #409eff;
  color: #fff;
}
</style>
