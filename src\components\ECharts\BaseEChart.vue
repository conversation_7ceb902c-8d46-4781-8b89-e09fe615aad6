<template>
  <div
    :id="chartId"
    :style="{ width: width, height: height }"
    class="echart-container"
  />
</template>

<script setup lang="ts">
import { ref, onMounted, watch, onBeforeUnmount, markRaw } from 'vue';
import * as echarts from 'echarts';
import type { ECharts, EChartsOption } from 'echarts';

const props = defineProps({
  options: {
    type: Object as () => EChartsOption,
    required: true,
  },
  width: {
    type: String,
    default: '100%',
  },
  height: {
    type: String,
    default: '300px',
  },
  id: {
    type: String,
    default: '',
  },
});

// 生成唯一ID
const uid = Math.floor(Math.random() * 10000);
const chartId = ref(props.id || `chart-${uid}`);
const chartInstance = ref<ECharts>();

// 初始化图表
const initChart = () => {
  if (!chartInstance.value) {
    const chartDom = document.getElementById(chartId.value);
    if (chartDom) {
      // 使用 markRaw 包装 echarts 实例，避免不必要的响应式
      chartInstance.value = markRaw(echarts.init(chartDom));

      // 添加被动事件监听器配置
      try {
        // 在初始化完成后添加鼠标事件监听器配置
        if (chartInstance.value) {
          chartDom.addEventListener('mousewheel', () => {}, { passive: true });
          chartDom.addEventListener('wheel', () => {}, { passive: true });
        }
      } catch (e) {
        console.warn('添加被动事件监听器失败:', e);
      }
    }
  }
  setOptions();
};

// 设置图表选项
const setOptions = () => {
  if (chartInstance.value) {
    chartInstance.value.setOption(props.options);
  }
};

// 窗口大小变化时，重新调整图表大小
const resizeChart = () => {
  if (chartInstance.value) {
    chartInstance.value.resize();
  }
};

// 监听配置变化，更新图表
watch(
  () => props.options,
  () => {
    setOptions();
  },
  { deep: true }
);

// 生命周期钩子
onMounted(() => {
  initChart();
  window.addEventListener('resize', resizeChart, { passive: true });
});

onBeforeUnmount(() => {
  if (chartInstance.value) {
    chartInstance.value.dispose();
    chartInstance.value = undefined;
  }
  window.removeEventListener('resize', resizeChart);
});

// 暴露给父组件的方法
defineExpose({
  getChartInstance: () => chartInstance.value,
});
</script>

<style scoped>
.echart-container {
  width: 100%;
  height: 100%;
}
</style>
