// @ts-nocheck
import Mock from 'mockjs';
import { MenuItem } from '@/api/system/menu';

// 忽略类型问题，启用编译
// @ts-ignore

// 模拟菜单数据
const menuList: MenuItem[] = [
  {
    id: 1,
    title: '系统管理',
    icon: 'Setting',
    path: '/system',
    component: 'Layout',
    sort: 0,
    hidden: false,
    children: [
      {
        id: 2,
        parentId: 1,
        title: '用户管理',
        icon: 'User',
        path: '/system/user',
        component: '/views/system/user/index',
        sort: 1,
        hidden: false
      },
      {
        id: 3,
        parentId: 1,
        title: '部门管理',
        icon: 'Collection',
        path: '/system/department',
        component: '/views/system/department/index',
        sort: 2,
        hidden: false
      },
      {
        id: 4,
        parentId: 1,
        title: '菜单管理',
        icon: 'Menu',
        path: '/system/menu',
        component: '/views/system/menu/index',
        sort: 3,
        hidden: false
      },
      {
        id: 10,
        parentId: 1,
        title: '角色管理',
        icon: 'Key',
        path: '/system/role',
        component: '/views/system/role/index',
        sort: 4,
        hidden: false
      }
    ]
  },
  {
    id: 5,
    title: '插件管理',
    icon: 'Setting',
    path: '/plugin',
    component: 'Layout',
    sort: 1,
    hidden: false,
    children: [
      {
        id: 6,
        parentId: 5,
        title: '插件用户',
        icon: 'User',
        path: '/plugin/user',
        component: '/views/plugin/user/index',
        sort: 1,
        hidden: false
      },
      {
        id: 7,
        parentId: 5,
        title: '套餐管理',
        icon: 'Document',
        path: '/plugin/package',
        component: '/views/plugin/package/index',
        sort: 2,
        hidden: false
      }
    ]
  },
  {
    id: 8,
    title: '支付管理',
    icon: 'Money',
    path: '/payment',
    component: 'Layout',
    sort: 2,
    hidden: false,
    children: [
      {
        id: 9,
        parentId: 8,
        title: '支付明细',
        icon: 'Document',
        path: '/payment/list',
        component: '/views/payment/index',
        sort: 1,
        hidden: false
      }
    ]
  }
];

// 获取最大 ID
let maxId = Math.max(...menuList.map(item => item.id), 
  ...menuList.flatMap(item => item.children?.map(child => child.id) || []));

// 返回成功响应
function resultSuccess<T>(data: T, msg = '操作成功'): ApiResponse<T> {
  return {
    code: 0,
    data,
    msg,
  };
}

// 返回错误响应
function resultError(msg = '操作失败', code = 500): ApiResponse<null> {
  return {
    code,
    data: null,
    msg,
  };
}

// 获取所有菜单列表
Mock.mock(/\/system\/menu\/list/, 'get', () => {
  // 扁平化菜单列表
  const flatMenus: MenuItem[] = [];
  
  menuList.forEach(menu => {
    flatMenus.push({ ...menu });
    if (menu.children && menu.children.length > 0) {
      menu.children.forEach(child => {
        flatMenus.push({ ...child });
      });
    }
  });
  
  return resultSuccess(flatMenus);
});

// 获取菜单树
Mock.mock(/\/system\/menu\/tree/, 'get', () => {
  return resultSuccess(JSON.parse(JSON.stringify(menuList)));
});

// 根据ID获取菜单
Mock.mock(/\/system\/menu\/\d+/, 'get', (options: any) => {
  const url = options.url;
  const id = parseInt(url.substring(url.lastIndexOf('/') + 1));
  
  // 查找菜单
  const findMenu = (id: number): MenuItem | null => {
    for (const menu of menuList) {
      if (menu.id === id) return { ...menu };
      if (menu.children) {
        for (const child of menu.children) {
          if (child.id === id) return { ...child };
        }
      }
    }
    return null;
  };
  
  const menu = findMenu(id);
  
  if (menu) {
    return resultSuccess(menu);
  } else {
    return resultError('菜单不存在');
  }
});

// 添加菜单
Mock.mock(/\/system\/menu$/, 'post', (options: any) => {
  const body = JSON.parse(options.body);
  
  if (!body.title || !body.path) {
    return resultError('标题和路径不能为空');
  }
  
  maxId++;
  const newMenu: MenuItem = {
    id: maxId,
    title: body.title,
    path: body.path,
    component: body.component || '',
    icon: body.icon || '',
    sort: body.sort || 0,
    hidden: body.hidden || false,
  };
  
  if (body.parentId) {
    newMenu.parentId = body.parentId;
    
    // 查找父菜单
    for (const menu of menuList) {
      if (menu.id === body.parentId) {
        if (!menu.children) menu.children = [];
        menu.children.push(newMenu);
        return resultSuccess(newMenu);
      }
      
      if (menu.children) {
        for (const child of menu.children) {
          if (child.id === body.parentId) {
            if (!child.children) child.children = [];
            child.children.push(newMenu);
            return resultSuccess(newMenu);
          }
        }
      }
    }
  }
  
  // 如果没有父菜单或找不到父菜单，添加为顶级菜单
  menuList.push(newMenu);
  
  return resultSuccess(newMenu);
});

// 更新菜单
Mock.mock(/\/system\/menu\/\d+/, 'put', (options: any) => {
  const body = JSON.parse(options.body);
  const url = options.url;
  const id = parseInt(url.substring(url.lastIndexOf('/') + 1));
  
  // 查找并更新菜单
  const updateMenu = (id: number, data: any): MenuItem | null => {
    for (const menu of menuList) {
      if (menu.id === id) {
        Object.assign(menu, data);
        return menu;
      }
      
      if (menu.children) {
        for (let i = 0; i < menu.children.length; i++) {
          if (menu.children[i].id === id) {
            Object.assign(menu.children[i], data);
            return menu.children[i];
          }
        }
      }
    }
    return null;
  };
  
  const menu = updateMenu(id, {
    title: body.title,
    path: body.path,
    component: body.component,
    icon: body.icon,
    sort: body.sort,
    hidden: body.hidden,
  });
  
  if (menu) {
    return resultSuccess(menu);
  } else {
    return resultError('菜单不存在');
  }
});

// 删除菜单
Mock.mock(/\/system\/menu\/\d+/, 'delete', (options: any) => {
  const url = options.url;
  const id = parseInt(url.substring(url.lastIndexOf('/') + 1));
  
  // 查找并删除菜单
  const deleteMenu = (id: number): boolean => {
    // 删除顶级菜单
    for (let i = 0; i < menuList.length; i++) {
      if (menuList[i].id === id) {
        menuList.splice(i, 1);
        return true;
      }
      
      // 删除子菜单
      if (menuList[i].children) {
        for (let j = 0; j < menuList[i].children.length; j++) {
          if (menuList[i].children[j].id === id) {
            menuList[i].children.splice(j, 1);
            return true;
          }
        }
      }
    }
    return false;
  };
  
  if (deleteMenu(id)) {
    return resultSuccess(true);
  } else {
    return resultError('菜单不存在');
  }
});

// 批量删除菜单
Mock.mock(/\/system\/menu\/batch/, 'delete', (options: any) => {
  const body = JSON.parse(options.body);
  const { ids } = body;
  
  if (!ids || !Array.isArray(ids)) {
    return resultError('请提供要删除的菜单 ID 数组');
  }
  
  let deleteCount = 0;
  ids.forEach(id => {
    // 查找并删除菜单
    const deleteMenu = (id: number): boolean => {
      // 删除顶级菜单
      for (let i = 0; i < menuList.length; i++) {
        if (menuList[i].id === id) {
          menuList.splice(i, 1);
          return true;
        }
        
        // 删除子菜单
        if (menuList[i].children) {
          for (let j = 0; j < menuList[i].children.length; j++) {
            if (menuList[i].children[j].id === id) {
              menuList[i].children.splice(j, 1);
              return true;
            }
          }
        }
      }
      return false;
    };
    
    if (deleteMenu(id)) {
      deleteCount++;
    }
  });
  
  return resultSuccess({ count: deleteCount });
});

// 设置菜单管理的 mock 数据
export const setupMenuMock = () => {
  console.log('菜单管理模拟数据已设置');
}; 